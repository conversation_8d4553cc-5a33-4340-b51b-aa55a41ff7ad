<template>
	<div>
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						:firstFocus="false"
						ref="scanPsw"
						v-model="searchData.batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="searchClick" />
				</el-form-item>
				<el-form-item label="" class="el-col el-col-4" label-width="10px">
					<el-button class="noShadow blue-btn" type="primary" @click="handleDel">移除</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<NavBar :nav-bar-list="barList" @handleClickItem="handleClick"></NavBar>
		<vTable :table="typeTable" @checkData="selectableFn" @getRowData="getRowData" checked-key="id"></vTable>
		<engravedCodeListDialog :dialogData="eCOptDialog"></engravedCodeListDialog>
		<FileUploadDialog
			:visible.sync="importFlag"
			:limit="1"
			title="刻字码数据导入"
			accept=".xlsx,.xls"
			@submit="submitUpload" />
	</div>
</template>

<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import engravedCodeListDialog from "../Dialog/engravedCodeListDialog";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import {
	selectBatchLetteringByBatchNumber,
	deleteBatchLettering,
	downloadTemplate,
	exportBatchLetteringList,
	importBatchLetteringList,
} from "@/api/qam/engravedCodeMsg";
import moment from "moment";
const barList = {
	title: "刻字码列表",
	list: [
		// {
		// 	Tname: "刻字码绑定",
		// 	Tcode: "bind",
		// 	icon: "nxinzeng",
		// 	event: "handleEngravedCode",
		// },
		// {
		// 	Tname: "刻字码绑定修改",
		// 	Tcode: "edit",
		// 	icon: "nxiugaizhushi",
		// 	event: "handleEngravedCodeEdit",
		// },
		// {
		// 	Tname: "刻字码修改",
		// 	Tcode: "editSerial",
		// 	icon: "nxiugaizhushi",
		// 	event: "handleEngravedCodeEdit",
		// },
		// {
		// 	Tname: "删除刻字码绑定",
		// 	Tcode: "del",
		// 	icon: "icon-delete",
		// 	event: "handleEngravedCodeDel",
		// },
		// {
		// 	Tname: "导入",
		// 	Tcode: "import",
		// 	event: "handleImport",
		// },
		// {
		// 	Tname: "导入模版下载",
		// 	Tcode: "importTemplate",
		// 	icon: "nshoujianjilu",
		// 	event: "handleImportTemplate",
		// },
		{
			Tname: "导出",
			Tcode: "export",
			event: "handleExport",
		},
		{
			Tname: "批次打印(刻字)",
			Tcode: "print",
			event: "batchPrint",
		},
		{
			Tname: "批次打印(序列)",
			Tcode: "serialNosPrint",
			event: "batchSerialNosPrint",
		},
		{
			Tname: "中微标签打印",
			Tcode: "AMECPrint",
			event: "AMECPrint",
		},
		{
			Tname: "ATC标签打印",
			Tcode: "ATCPrint",
			event: "ATCPrint",
		},
	],
};

export default {
	name: "engravedCodeMsg",
	components: {
		vTable,
		NavBar,
		ScanCode,
		engravedCodeListDialog,
		FileUploadDialog,
	},
	data() {
		return {
			searchData: {
				batchNumber: "",
			},
			importFlag: false,
			barList,
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
						width: "206",
					},
					{ label: "刻字码", prop: "letteringNo", width: "120" },
					{
						label: "序列号",
						prop: "serialNo",
						width: "120",
					},
					{
						label: "物料编码",
						prop: "partNo",
					},
					{
						label: "产品图号",
						prop: "innerProductNo",
					},
					{
						label: "制造番号",
						prop: "makeNo",
					},
					{
						label: "图号版本",
						prop: "innerProductVer",
						width: "120",
					},
					{
						label: "产品英文名",
						prop: "productNameEn",
						width: "120",
					},
					{
						label: "客户图号",
						prop: "customerProductNo",
						width: "120",
					},
					{
						label: "客户图号版本",
						prop: "customerProductNoVer",
						width: "120",
					},
					{
						label: "工单号",
						prop: "workOrderCode",
					},
					{
						label: "更新人",
						prop: "updatedBy",
					},
					{
						label: "更新时间",
						prop: "updatedTime ",
						render(item) {
							return moment(item.updatedTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime ",
						render(item) {
							return moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
				],
			},
			//弹框配置
			eCOptDialog: {
				visible: false,
				bindStatus: "",
				editData: [],
				title: "",
			},

			rowData: [],
		};
	},
	created() {
		// this.initTableData();
	},
	methods: {
		searchClick() {
			this.initTableData();
		},
		async initTableData() {
			const { data } = await selectBatchLetteringByBatchNumber(this.searchData);
      // 用 lodash 去重
      const uniqueData = _.uniqBy([...this.typeTable.tableData, ...data], "id");
			this.typeTable.tableData = uniqueData
		},

		handleClick(val) {
			this[val.event] && this[val.event](val);
		},
		batchPrint() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(this.rowData));
			window.open(location.href.split("/#/")[0] + "/#/batchList/batchPDF417Print");
		},
		batchSerialNosPrint() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			let printList = this.rowData.filter((item) => 
				item.letteringNo != item.serialNo
			);
			if (printList.length <= 0) {
				return this.$message.warning("请选择刻字码与序列号不同的数据");
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(printList));
			window.open(location.href.split("/#/")[0] + "/#/batchList/batchSeriaNoPrint");
		},
		handleImportTemplate() {
			downloadTemplate({}).then((res) => {
				this.$download("", "刻字码模板.xlsx", res);
			});
		},
		AMECPrint() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(this.rowData));
			window.open(location.href.split("/#/")[0] + "/#/batchList/AMECTagPrint");
		},
		ATCPrint() { 
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(this.rowData));
			window.open(location.href.split("/#/")[0] + "/#/batchList/ATCTagPrint");
		},
		handleImportTemplate() {
			downloadTemplate({}).then((res) => {
				this.$download("", "刻字码模板.xlsx", res);
			});
		},
		handleExport() {
      const batchNumberList = this.typeTable.tableData.map((item) => item.batchNumber);
			exportBatchLetteringList({batchNumberList}).then((res) => {
				this.$download("", "刻字码.xlsx", res);
			});
		},
		handleImport() {
			this.importFlag = true;
		},
		async submitUpload(formData) {
			if (this.$isEmpty(formData.fileList, "请选择文件后进行上传~")) return;
			try {
				const prama = new FormData();
				prama.append("file", formData.fileList[0].raw);
				await this.$responseMsg(await importBatchLetteringList(prama));
			} catch (e) {
			} finally {
				this.importFlag = false;
			}
			this.searchClick();
		},
		async handleEngravedCode() {
			this.eCOptDialog.visible = true;
			this.eCOptDialog.bindStatus = "add";
			this.eCOptDialog.title = "刻字码绑定";
		},
		handleEngravedCodeDel() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			this.$confirm("确定要删除吗？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			})
				.then(async () => {
					const params = this.rowData.map((item) => {
						return item.id;
					});
					const {
						status: { code, message },
					} = await deleteBatchLettering(params);
					if (code !== 200) {
						return this.$message.error(message);
					}
					this.eCOptDialog.editData = [];
					this.$message.success("删除刻字码成功");
					this.initTableData();
				})
				.catch(() => {});
		},
		handleEngravedCodeEdit(val) {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			this.eCOptDialog.bindStatus = "edit";
			this.eCOptDialog.title = val.Tname;
			this.eCOptDialog.visible = true;
		},
		selectableFn(val) {
			// console.log(val);
		},
		getRowData(val) {
			this.rowData = val;
			this.eCOptDialog.editData = val;
		},

		handleDel() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			this.rowData.forEach((rowItem) => {
				const index = this.typeTable.tableData.findIndex((item) => item.id === rowItem.id);
				if (index !== -1) {
					this.typeTable.tableData.splice(index, 1);
				}
			});
		},
	},
};
</script>

<style lang="scss" scoped></style>
