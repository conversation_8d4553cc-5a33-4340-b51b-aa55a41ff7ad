/*
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-10-17 11:19:31
 * @LastEditTime: 2025-05-15 14:27:08
 */
import request from "@/config/request.js";
export function getScrapListByPage(data) {
  //获取报废批次列表
	return request({
		url: "/ppBatchScrap/getPage",
		method: "post",
		data,
	});
}
export function getStartScrapBill(data) {
  //创建报废单
	return request({
		url: "/ppScrapBill/startBill",
		method: "post",
		data,
	});
}
export function getSaveScrapBill(data) {
  //暂存报废单
	return request({
		url: "/ppScrapBill/save",
		method: "post",
		data,
	});
}

export function getScrapBillById(data) {
  //获取报废单详情
	return request({
		url: "/ppScrapBill/getByScrapNo",
		method: "get",
		data,
	});
}

export function getScrapCurMonthRes(data) {
  //获取当月统计
	return request({
		url: "/ppBatchScrap/getCurMonthRes",
		method: "get",
		data,
	});
}

export function getDimensionByBatch(data) {
  //根据批次获取不合格尺寸
	return request({
		url: "/ppBatchScrap/getDimensionByBatch",
		method: "get",
		data,
	});
}

export function getCancelScrap(data) {
  //取消报废
	return request({
		url: "/ppBatchScrap/cancelScrap",
		method: "post",
		data,
	});
}

export function getBackToNG(data) {
  //返回不良判定
	return request({
		url: "/ppBatchScrap/back2NG",
		method: "post",
		data,
	});
}

export function getScrapAppend(data) {
  //报废追加
	return request({
		url: "/ppBatchScrap/scrapAppend",
		method: "post",
		data,
	});
}
export function getScrapCancelAppend(data) {
  //取消追加
	return request({
		url: "/ppBatchScrap/cancelAppend",
		method: "post",
		data,
	});
}

export function getScrapExport(data) {
  //报废批次导出
	return request({
		url: "/ppBatchScrap/export",
		method: "post",
		data,
        responseType: "blob",
        timeout:1800000
	});
}

export function getUploadNgPic(data) {
  //报废批次导出
	return request({
		url: "/ppScrapBill/uploadNgPic",
		method: "post",
		data,
	});
}

export function getScanOneBatchScrap(data) {
  //报废批次扫码
	return request({
		url: "/ppBatchScrap/scanOneBatchScrap",
		method: "get",
		data,
	});
}

export function getScrapNotAppend(data) {
  //报废不追加
	return request({
		url: "/ppBatchScrap/scrapNotAppendMany",
		method: "post",
		data,
	});
}
export async function searchDict(data) { // 查询下拉框
  return await request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

//查询组织信息——所有部门层级
export function selectOrganizationDepartment(data) {
	return request({
		url: "/organization/select-organization-department",
		method: "post",
		data,
	});
}


