/*
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2025-04-16 15:06:45
 * @LastEditTime: 2025-06-06 17:15:26
 */
import request from "@/config/request.js";
// 查询任务列表
export function findTaskPage(data) {
	return request({
		url: "/iqcTask/findTaskPage",
		method: "post",
		data,
	});
}
// 新增任务
export function getCreateTask(data) {
	return request({
		url: "/iqcTask/createTask",
		method: "post",
		data,
	});
}
// 修改任务
export function getUpdateTask(data) {
	return request({
		url: "/iqcTask/updateTask",
		method: "post",
		data,
	});
}

// 删除任务
export function getDeleteTask(data) {
	return request({
		url: "/iqcTask/deleteTask",
		method: "get",
		data,
	});
}

export function getExportTaskList(data) {
	return request({
		url: "/iqcTask/exportTaskList",
		method: "post",
		data,
    responseType: "blob",
    timeout: 1800000,
	});
}


// 查询检验标准模板
export function getFindStdList(data) {
	return request({
		url: "/iqcStd/findStdList",
		method: "post",
		data,
	});
}

//获取来料检任务单产品列表
export function getFindTaskInfoPage(data) {
	return request({
		url: "/iqcTask/findTaskInfoPage",
		method: "post",
		data,
	});
}

//获取检验标准列表
export function getFindStdPage(data) {
	return request({
		url: "/iqcStd/findStdPage",
		method: "post",
		data,
	});
}
//新增检验标准
export function getCreateStd(data) {
	return request({
		url: "/iqcStd/createStd",
		method: "post",
		data,
	});
}
//修改检验标准
export function getUpdateStd(data) {
	return request({
		url: "/iqcStd/updateStd",
		method: "post",
		data,
	});
}
//删除检验标准
export function getDeleteStd(data) {
	return request({
		url: "/iqcStd/deleteStd",
		method: "get",
		data,
	});
}
//新增检验数据
export function getCreateStdItem(data) {
	return request({
		url: "/iqcStd/createStdItemOne",
		method: "post",
		data,
	});
}

//修改检验数据
export function getUpdateStdItem(data) {
	return request({
		url: "/iqcStd/updateStdItem",
		method: "post",
		data,
	});
}
//获取检验标准详情
export function getStdItemListByStdId(data) {
  return request({
		url: "/iqcStd/findStdById",
		method: "get",
		data,
	});
}
//上传图片
export function getUploadImage(data) {
  return request({
		url: "/iqcStd/upload",
		method: "post",
		data,
	});
}
//增加工具
export function getCreateStdTool(data) {
  return request({
		url: "/iqcStd/createStdToolOne",
		method: "post",
		data,
	});
}
//修改工具
export function getUpdateStdTool(data) {
  return request({
		url: "/iqcStd/updateStdTool",
		method: "post",
		data,
	});
}
//删除检验数据标准
export function getDeleteStdItems(data) {
	return request({
		url: "/iqcStd/deleteStdItems",
		method: "post",
		data,
	});
}
//删除工具
export function getDeleteStdTools(data) {
	return request({
		url: "/iqcStd/deleteStdTools",
		method: "post",
		data,
	});
}
//根据任务id查询产品检验详情
export function getFindTaskInfoById(data) {
  return request({
    url: "/iqcTask/findTaskInfoById",
    method: "get",
    data,
  });
}

//修改产品检验详情
export function getUpdateTaskInfo(data) {
  return request({
    url: "/iqcTask/updateTaskInfo",
    method: "post",
    data,
  });
}

//获取当月统计数据
export function getIqcTaskStatistic(data) {
  return request({
    url: "/iqcTask/getStatistic",
    method: "get",
    data,
  });
}

//导出检验报告
export function getExportTaskRpt(data) {
  return request({
    url: "/iqcTask/exportTaskRpt",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
//导出产品检验列表
export function getExportTaskInfoList(data) {
  return request({
    url: "/iqcTask/exportTaskInfoList",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
// 根据类型集合查询字典表
export function searchDD(data) {
  return request({
    url: "/fsysDict/select-dictlist",
    method: "post",
    data, //{typeList:[]}    =>{ dictCode: '编码',dictCodeValue:	'编码名称' }
  });
}

//获取来料检详情
export function getFindTaskInfoByTaskNoAndSortNo(data) {
  return request({
    url: "/iqcTask/findTaskInfoByTaskNoAndSortNo",
    method: "post",
    data,
  });
}

//复制任务
export function getCopyTask(data) {
  return request({
    url: "/iqcTask/copyTask",
    method: "post",
    data,
  });
}

export function getFindFlatTaskById(data) {
  return request({
    url: "/iqcTask/findFlatTaskById",
    method: "get",
    data,
  });
}

export function getConfirmTaskById(data) {
  return request({
    url: "/iqcTask/confirmTask",
    method: "get",
    data,
  });
}

export function getDownloadTaskTemplate(data) {
  return request({
    url: "/iqcTask/downloadTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
export function getUploadTaskTemplate(data) {
  return request({
    url: "/iqcTask/importByTemplate",
    method: "post",
    data,
  });
}

// 查询治、工具列表
export function getFindIqcToolPage(data) {
	return request({
		url: "/iqcStd/findIqcToolPage",
		method: "post",
		data,
	});
}