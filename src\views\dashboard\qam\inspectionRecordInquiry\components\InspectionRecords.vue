<template>
	<div>
		<el-form ref="searchForm" class="" :model="searchData" @submit.native.prevent>
			<el-form-item class="el-col el-col-6" label="制造番号" label-width="80px" prop="makeNo">
				<el-input v-model="searchData.makeNo" clearable placeholder="请输入制造番号" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="状态" label-width="80px" prop="status">
				<el-select v-model="searchData.status" clearable filterable placeholder="请选择状态">
					<el-option
						v-for="item in PP_INSPECTION_TASK_STATUS"
						:key="item.dictCode"
						:label="item.dictCodeValue"
						:value="item.dictCode"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="是否合格" label-width="80px" prop="result">
				<el-select v-model="searchData.result" clearable filterable placeholder="请选择是否合格">
					<el-option
						v-for="item in PP_INSPECTION_TASK_RESULT"
						:key="item.dictCode"
						:label="item.dictCodeValue"
						:value="item.dictCode"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
				<ScanCode
					class="auto-focus"
					ref="scanPsw"
					v-model="searchData.batchNumber"
					placeholder="扫描录入（批次号）"
					@enter="scanEnter" />
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="创建时间" label-width="80px" prop="createdTime">
				<el-date-picker
					v-model="searchData.createdTime"
					clearable
					type="datetimerange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="timestamp"
					:default-time="['00:00:00', '23:59:59']" />
			</el-form-item>
			<el-form-item class="el-col el-col fr">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					native-type="submit"
					@click.prevent="searchHandler">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<!-- 检验任务列表 -->
		<nav-bar :nav-bar-list="navgro" @handleClick="navClickInspection" />
		<v-table
			:table="inspectionTable"
			@changePages="changePages"
			@checkData="getCurRow"
			@getRowData="getRowData"
			@dbCheckData="dbCheckData"
			@changeSizes="grochangeSize"
			checked-key="id">
			<div slot="qmsStatus" slot-scope="{ row }">
				<div
					:style="{
						background: row.qmsStatus == 1 ? '#ff4d4f' : '',
						color: row.qmsStatus == 1 ? '#fff' : '#000',
						padding: '2px',
						borderRadius: '4px',
					}">
					{{ checkType(INSPECT_QMS_STATUS(), row.qmsStatus) }}
				</div>
			</div>
		</v-table>
		<el-tabs v-model="activeName">
			<el-tab-pane label="MMS检验项" name="mmsInfo">
				<MMSInspectionList
					:cur-row="curRow"
					:PP_INSPECTION_TASK_RESULT="PP_INSPECTION_TASK_RESULT"
					:appearance-bad-causes="appearanceBadCauses"
					:other-bad-causes="otherBadCauses"
					@update:mmsRow="updateMmsRow" />
			</el-tab-pane>

			<el-tab-pane label="QMS检验项" name="equipmentInfo">
				<QMSInspectionList :cur-row="curRow" />
			</el-tab-pane>
		</el-tabs>
    
		<NgDialog
			:dialogData="ngOptDialog"
			:tableData="inspectionResultList"
			:tableSingleData="tableSingleData"></NgDialog>
    <BatchProcessDialog :dialogData="batchProcessDialog" >  </BatchProcessDialog>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1.vue";
import NgDialog from "@/views/dashboard/components/defectiveProductHandl/ngDialog";
import MMSInspectionList from "./MMSInspectionList.vue";
import QMSInspectionList from "./QMSInspectionList.vue";
import  BatchProcessDialog from "../Dialog/batchProcessDialog.vue";
import { MessageBox } from "element-ui";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import { searchDD } from "@/api/api.js";
import {
	getInspectionData,
	editInspectionResult,
	judgmentNGCharge,
	pullQMSInspectionFile,
	fPpInspectionTaskExport,
	getMMSNGData,
} from "@/api/qam/inspectionRecordInquiry";

const KEY_METHODS = new Map([
	["ok", "inspectionOk"],
	["ng", "inspectionNg"],
	["pullQMS", "pullQMSFile"],
	["viewQMS", "viewQMSReport"],
	["export", "handelExport"],
	["batchProcess", "handelBatchProcess"],
]);

export default {
	name: "InspectionRecords",
	components: {
		NavBar,
		vTable,
		NgDialog,
		ScanCode,
		MMSInspectionList,
		QMSInspectionList,
    BatchProcessDialog
	},
	inject: ["PAGETYPE", "SHOWlABLE", "INSPECT_QMS_STATUS"],
	data() {
		return {
			searchData: {
				makeNo: "",
				batchNumber: "",
				status: "",
				result: "",
				createdTime: [],
			},
			inspectionTable: {
				tableData: [],
				sequence: true,
				check: true,
				count: 1,
				size: 10,
				total: 0,
				tabTitle: [
					{
						label: `${this.SHOWlABLE().taskCode}`,
						prop: "taskCode",
					},
					{
						label: "产品编码",
						prop: "partNo",
						width: "150",
					},
					{
						label: "检验类型",
						prop: "taskType",
						width: "150",
					},
					{
						label: "工序名称",
						prop: "stepName",
						width: "150",
					},
					{
						label: "工艺路线编码",
						prop: "routeCode",
						width: "150",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
						width: "150",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
						width: "150",
					},
					{
						label: "工单号",
						prop: "orderNo",
						width: "150",
					},
					{
						label: "制造番号",
						prop: "makeNo",
						width: "150",
					},
					{
						label: "批次号",
						prop: "batchNumber",
						width: "206",
					},
					{
						label: "状态",
						prop: "status",
						render: (row) => this.checkType(this.PP_INSPECTION_TASK_STATUS, row.status),
					},
          {
						label: "委外单号",
						prop: "outsourcingNo",
					},
					{
						label: "检验结果",
						prop: "result",
						render: (row) => {
							if (row.result === null) {
								return "暂无数据";
							}
							return this.checkType(this.PP_INSPECTION_TASK_RESULT, row.result.toString());
						},
					},
					{
						label: "QMS质检报告",
						prop: "fileAddress",
						width: "120",
						render: (row) => {
							return row.fileAddress ? "已有" : "暂无";
						},
					},
					{
						label: "QMS状态",
						prop: "qmsStatus",
						width: "90",
						slot: true,
						// render: (row) => {
						//   if (row.qmsStatus === null) {
						// 		return "暂无";
						// 	}
						//   return this.checkType(this.INSPECT_QMS_STATUS(), row.qmsStatus.toString());
						// }
					},
					{
						label: "QMS拒检人帐号",
						prop: "rejectByCode",
					},
					{
						label: "QMS拒检人姓名",
						prop: "rejectByName",
					},
					{
						label: "QMS拒检原因",
						prop: "rejectReason",
					},
					{
						label: "创建人",
						prop: "createdBy",
						render: (row) => this.$findUser(row.createdBy),
					},
					{
						label: "创建时间",
						prop: "createdTime",
						width: "150",
						render: (r) => formatYS(r.createdTime),
					},
					{
						label: "最后修改人",
						prop: "updatedBy",
						width: "150",
						render: (row) => this.$findUser(row.updatedBy),
					},
					{
						label: "最后修改时间",
						prop: "updatedTime",
						width: "150",
						render: (r) => formatYS(r.updatedTime),
					},
				],
			},
			curRow: {},
			mmsRow: {},
			activeName: "mmsInfo",
			PP_INSPECTION_TASK_STATUS: [],
			PP_INSPECTION_TASK_RESULT: [],
			appearanceBadCauses: [],
			otherBadCauses: [],
			ngOptDialog: {
				visible: false,
				itemData: {},
				multiple: false,
			},
			batchProcessDialog: {
				visible: false,
			},
			rowList: [],
			tableSingleData: {},
			inspectionResultList: [],
		};
	},
	computed: {
		navgro() {
			const list = [
				{
					Tname: "OK",
					key: "ok",
					Tcode: "ok",
				},
				{
					Tname: "NG",
					key: "ng",
					Tcode: "ng",
				},
				{
					Tname: "批量处理",
					key: "batchProcess",
					Tcode: "batchProcess",
				},
				{
					Tname: "拉取QMS质检报告",
					key: "pullQMS",
					Tcode: "pullQMS",
				},
			
				{
					Tname: "导出",
					key: "export",
					Tcode: "export",
				},
			];
			return {
				title: "检验任务列表",
				list: list,
			};
		},
	},
	watch: {
		rowList(val) {
			if (val.length === 0) {
				this.curRow = {};
			}
		},
	},
	methods: {
		async getDD() {
			return searchDD({ typeList: ["PP_INSPECTION_TASK_RESULT", "PP_INSPECTION_TASK_STATUS"] }).then((res) => {
				this.PP_INSPECTION_TASK_STATUS = res.data.PP_INSPECTION_TASK_STATUS;
				this.PP_INSPECTION_TASK_RESULT = res.data.PP_INSPECTION_TASK_RESULT;
			});
		},
		async getAppearanceBadCauses() {
			const { data } = await getMMSNGData({ data: { type: "5" } });
			this.appearanceBadCauses = data;
		},
		async getOtherBadCauses() {
			const { data } = await getMMSNGData({ data: { type: "1" } });
			this.otherBadCauses = data;
		},
		checkType(arr, str) {
			const obj = arr.find((item) => item.dictCode == String(str));
			return obj ? obj.dictCodeValue : str;
		},
		scanEnter(val) {
			this.searchData.batchNumber = val;
			if (!val) {
				return this.$message.warning("请输入/扫码(批次号)");
			}
			this.getData();
		},
		grochangeSize(val) {
			this.inspectionTable.size = val;
			this.searchHandler();
		},
		navClickInspection(key) {
			const method = KEY_METHODS.get(key);
			method && this[method] && this[method]();
		},
		getCurRow(row) {
			if (this.$isEmpty(row, "", "id")) return;
			this.curRow = row;
		},
		updateMmsRow(row) {
			this.mmsRow = row;
		},
		checkInspectionType() {
			const taskTypeList = this.rowList.map((item) => {
				return item.taskType;
			});
			if (taskTypeList.length === 0) {
				return true;
			}
			const firstTaskType = taskTypeList[0];
			return taskTypeList.every((taskType) => taskType === firstTaskType);
		},
		inspectionOk() {
			if (this.$isEmpty(this.rowList, "请选择一条检验任务")) return;
			const checkVal = this.checkInspectionType();
			if (!checkVal) {
				return this.$message.warning("请选择相同检验类型的任务");
			}
			const selectList = this.rowList.map((item) => {
				return {
					...item,
					result: 1,
				};
			});
			this.editInspectionResult(selectList);
		},
		async editInspectionResult(parameter) {
			try {
				const res = await editInspectionResult(parameter);
				if (res.status.success) {
					this.$showSuccess(res.status.message);
					this.getData();
					return res;
				} else {
					this.$showWarn(res.status.message);
					return false;
				}
			} catch (error) {
				console.error("Error in editInspectionResult:", error);
				return false;
			}
		},
		inspectionNg() {
			if (this.$isEmpty(this.rowList, "请选择一条检验任务")) return;

			const checkVal = this.checkInspectionType();
			if (!checkVal) {
				return this.$message.warning("请选择相同检验类型的任务");
			}
			try {
				const taskType = this.rowList[0].taskType;
				const foxPro = taskType == "委外工检" ? "当前为委外任务，是否确定NG" : "是否确认NG?";
				this.handleCofirm(foxPro).then(async () => {
					const {
						data,
						status: { message },
					} = await judgmentNGCharge(this.rowList);
					if (data) {
						const selectList = this.rowList.map((item) => {
							return {
								...item,
								result: 0,
							};
						});
						this.getData();
						const res = await this.editInspectionResult(selectList);
						if (res.data && taskType !== "委外工检") {
							this.tableSingleData = res.data[0];
							this.inspectionResultList = res.data[0].batchRejectInfoList;
							this.ngOptDialog.visible = true;
						}
					} else {
						this.$message.warning(message);
					}
				});
			} catch (e) {}
		},
    handelBatchProcess(){
      this.batchProcessDialog.visible = true;
    },
		handleCofirm(text = "是否确认NG?", type = "warning", confirmText = "确定") {
			return MessageBox.confirm(text, "提示", {
				customClass: "wrap-line",
				confirmButtonText: confirmText,
				dangerouslyUseHTMLString: true,
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				showClose: false,
				closeOnClickModal: false,
				closeOnPressEscape: false,
				type: type,
				center: false,
			});
		},
	
		searchHandler() {
			this.getData();
		},
		resetHandler() {
			this.$refs.searchForm.resetFields();
		},
		changePages(val) {
			this.inspectionTable.count = val;
			this.getData();
		},
		handelExport() {
			const params = {
				data: {
					taskStepType: this.PAGETYPE(),
					pn: this.searchData.pn,
					productDirection: this.searchData.productDirection,
					makeNo: this.searchData.makeNo,
					batchNumber: this.searchData.batchNumber,
					status: this.searchData.status,
					result: this.searchData.result,
					createdTimeEnd: !this.searchData.createdTime
						? null
						: formatTimesTamp(this.searchData.createdTime[1]),
					createdTimeStart: !this.searchData.createdTime
						? null
						: formatTimesTamp(this.searchData.createdTime[0]),
				},
			};
			fPpInspectionTaskExport(params).then((res) => {
				this.$download("", `${this.SHOWlABLE().recordLable}导出.xlsx`, res);
			});
		},
		async getData() {
			try {
				const requestData = {
					taskStepType: this.PAGETYPE(),
					pn: this.searchData.pn,
					productDirection: this.searchData.productDirection,
					makeNo: this.searchData.makeNo,
					batchNumber: this.searchData.batchNumber,
					status: this.searchData.status,
					result: this.searchData.result,
					createdTimeEnd: !this.searchData.createdTime
						? null
						: formatTimesTamp(this.searchData.createdTime[1]),
					createdTimeStart: !this.searchData.createdTime
						? null
						: formatTimesTamp(this.searchData.createdTime[0]),
				};

				const { data, page } = await getInspectionData({
					data: requestData,
					page: {
						pageNumber: this.inspectionTable.count,
						pageSize: this.inspectionTable.size,
					},
				});

				if (data) {
					this.inspectionTable.tableData = data;
					this.inspectionTable.total = page.total || 0;
				}
			} catch (e) {
				console.error("获取数据时发生错误:", e);
			}
		},
		async pullQMSFile() {
      if(this.$isEmpty(this.curRow, "请选择一条检验任务", "id")) return;
			try {
				const { data, status } = await pullQMSInspectionFile([
					{ BatchCode: this.curRow.batchNumber, MMSTaskCode: this.curRow.taskCode },
				]);
				if (data) {
					this.$message.success("操作成功");
					this.getData();
				} else {
					this.$message.warning(status.message);
				}
			} catch (e) {
				console.error("拉取QMS质检报告时发生错误:", e);
			}
		},
		dbCheckData(row) {
			this.$emit("dbCheckData", row);
		},
		getRowData(rows) {
			this.rowList = rows;
			this.$emit("getRowData", rows);
		},
	},
	created() {
		// this.getData();
		this.getDD();
		this.getAppearanceBadCauses();
		this.getOtherBadCauses();
	},
};
</script>
