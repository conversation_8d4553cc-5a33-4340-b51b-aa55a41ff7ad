<template>
  <el-dialog
    :title="dialogTitle"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddInspectionDialog"
  >
    <div class="mt10 flex1">
      <el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom" :rules="addInspectionRule">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="检验任务名称" label-width="120px" prop="taskName">
						<el-input v-model.trim="ruleFrom.taskName" clearable placeholder="请输入检验任务名称" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="检验模板" label-width="80px" prop="stdId">
            <el-select
              v-model="ruleFrom.stdId"
              @change="setBillCode"
              placeholder="请选择检验模板 "
              clearable
              filterable
            >
              <el-option
                v-for="item in stdTemList"
                :key="item.id"
                :label="item.stdName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="参考图" class="el-col el-col-11" label-width="120px">
            <div class="row">
              <el-image v-if="imageUrl" class="ex-image" :src="imageUrl" :preview-src-list="[imageUrl]"> </el-image>
            </div>
          </el-form-item>
          <el-form-item v-if="ruleFrom.stdId&&currentStdName.indexOf('晶碇') != -1" class="el-col el-col-11" label="图形" label-width="80px" prop="map">
            <el-select
              v-model="ruleFrom.map"
              placeholder="请选择图形 "
              clearable
              filterable
            >
              <el-option
                v-for="item in mapList"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="规格/材质" label-width="100px" prop="mrmodel">
						<el-input v-model="ruleFrom.mrmodel" placeholder="请输入规格/材质" />
					</el-form-item>
          
          <el-form-item class="el-col el-col-11" label="检验表编号" label-width="100px" prop="billCode">
						<el-input v-model.trim="ruleFrom.billCode" disabled placeholder="请输入检验表编号" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="发票号" label-width="80px" prop="ticket">
						<el-input v-model.trim="ruleFrom.ticket"  placeholder="请输入发票号" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="存货编码" label-width="80px" prop="partNo">
						<el-input v-model.trim="ruleFrom.partNo"  placeholder="请输入存货编码" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="供应商" label-width="80px" prop="supplierName">
						<el-input v-model.trim="ruleFrom.supplierName"  placeholder="请输入供应商" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="LOT NO" label-width="80px" prop="lotNo">
						<el-input v-model.trim="ruleFrom.lotNo"  placeholder="请输入LOT NO" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="内部图号" label-width="80px" prop="innerProductNo">
						<el-input
							v-model.trim="ruleFrom.innerProductNo"
							clearable
							placeholder="请输入内部图号" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="最终客户" label-width="80px" prop="customerName">
						<el-input v-model.trim="ruleFrom.customerName"  placeholder="请输入最终客户" />
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="产品名称" label-width="80px" prop="productName">
						<el-input v-model="ruleFrom.productName"  placeholder="请输入产品名称" />
					</el-form-item>
          <el-form-item v-if="isEdit" class="el-col el-col-11" label="COC" label-width="80px" prop="ifCoc">
						<el-checkbox v-model="ruleFrom.ifCoc"></el-checkbox>
					</el-form-item>
          <el-form-item class="el-col el-col-16" label="是否为合格供应商" label-width="140px" prop="ifGoodSupplier">
            <el-select
              v-model="ruleFrom.ifGoodSupplier"
              placeholder="请选择是否为合格供应商 "
              clearable
              filterable
            >
              <el-option
                v-for="item in ifGoodSupplierList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitInspection('proPFrom')">{{ submitBtn }}</el-button>
      <el-button class="noShadow red-btn" @click="closeAddInspection">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { getCreateTask,getUpdateTask ,getFindStdList } from "@/api/qam/incomingInspection.js";
export default {
  components: {
    NavBar,
    vTable,
  },
  props: {
    showAddInspectionDialog: {
      type: Boolean,
      default: false,
    },
    // 是否是修改
    isEdit: {
      type: Boolean,
      default: false,
    },
    // 要修改的任务信息
    currentInspectionRow: {
      type: Object,
      default: () => {
        return {};
      },
    },
    stdTemList: {
      type: Array,
      default: () => [],
    },
  },
  created() {
    this.init();
  },
  data() {
    return {
      addInspectionRule: {
        taskName: [{ required: true, message: "请输入检验任务名称" }],
        stdId: [{ required: true, message: "请选择检验模板" }],
        map: [{ required: true, message: "请选择图形" }],
      },
      ifGoodSupplierList: [
        { label: "是", value: "0" },
        { label: "否", value: "1" },
      ],
      ruleFrom: {
        taskName: "",
        taskNo: "",
        stdId: "",
        billCode: "",
        ticket: "",
        partNo: "",
        supplierName: "",
        lotNo: "",  
        innerProductNo: "",
        customerName: "",
        ifGoodSupplier: "",
        mrmodel: "",
        map:'',
        ifCoc: "",
        productName:""

      },
      dialogTitle: "",
      submitBtn: "保存",
      imageUrl: "",
      mapList: [
        { dictCodeValue: "图Ⅰ", dictCode: "图Ⅰ" },
        { dictCodeValue: "图Ⅱ", dictCode: "图Ⅱ" },
        { dictCodeValue: "图Ⅲ", dictCode: "图Ⅲ" },
        { dictCodeValue: "图Ⅳ", dictCode: "图Ⅳ" },
      ],
      currentStdName: "",
    };
  },
  methods: {
    setBillCode(val){
      if(val){
        this.ruleFrom.billCode = this.stdTemList.find(item => item.id === val).billCode
        this.currentStdName = this.stdTemList.find(item => item.id === val).stdName
        this.imageUrl = this.$getFtpPath(this.stdTemList.find(item => item.id === val).picUrl);
      }
    },
    init() {
      
      if (this.isEdit) {
        this.submitBtn = "修改";
        this.dialogTitle = "修改来料检任务";
        console.log("currentInspectionRow", this.currentInspectionRow);
        const result = _.assign(this.ruleFrom, _.pickBy(this.currentInspectionRow, (value, key) => key in this.ruleFrom));
        result.ifCoc = result.ifCoc === '1' ? false : true;
        this.ruleFrom = result;
      } else {
        this.submitBtn = "创建";
        this.dialogTitle = "创建来料检任务";
      }
    },

    
    submitInspection(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (this.isEdit) {
              this.ruleFrom.id = this.currentInspectionRow.id;
              this.ruleFrom.ifCoc = this.ruleFrom.ifCoc ? "0" : "1";
              let params = Object.assign(this.currentInspectionRow,this.ruleFrom);
              getUpdateTask(params).then((res) => {
                // 处理成功提示信息
                this.$responsePrecedenceMsg(res).then(() => {
                  this.$emit("addInspectionHandle");
                  this.$emit("update:showAddInspectionDialog", false);
                });
              });
            } else {
              let param = {
                ...this.ruleFrom,
              };
              delete param.ifCoc;
              getCreateTask(param).then((res) => {
                // 处理成功提示信息
                this.$responsePrecedenceMsg(res).then(() => {
                  this.$emit("addInspectionHandle");
                  this.$emit("update:showAddInspectionDialog", false);
                });
              });
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    closeAddInspection() {
      this.$emit("update:showAddInspectionDialog", false);
    },
  },
};
</script>
<style lang="scss" scoped>
.custom-cell {
  padding: 0px 10px 0px 0px;
  width: 100%;
}
.ex-image {
  width: 200px;
}
</style>
