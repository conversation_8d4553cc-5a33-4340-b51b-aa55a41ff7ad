<template>
	<!-- 最终合格率按产品分布 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="left">
				<div class="echartsBox">
					<Echart id="FinalPassRateTendTotal" :flag="true" :data="BarOption" height="400px" />
				</div>
			</div>
		</section>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
          ref="FinalPassRateTendTotal"
					:table="listTable"
					checked-key="id"
					@getRowData="checkData"
					@checkData="selectData"
					@changePages="changePage"
					@changeSizes="changeSize" />
			</div>
		</section>
    <el-dialog
		class="batch-operate-dialog"
		title="设置目标合格率"
		width="25%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showSetRateDialog">
		<div class="mt10 flex1">
			<el-form ref="rateForm" :model="rateModel" class="demo-ruleForm" :rules="rule">
				<el-row>
					<el-form-item class="el-col el-col-22" label="目标合格率（%）" label-width="140px" prop="rate">
						<el-input
							type="number"
							v-model="rateModel.rate"
							clearable
							placeholder="请输入目标合格率"></el-input>
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="setRate">确定</el-button>
			<el-button class="noShadow red-btn" @click="()=>{showSetRateDialog = false}">取 消</el-button>
		</div>
	</el-dialog>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import Echart from "@/components/echartsAll/echarts.vue";

import {
	getRptMonthOkRate,
  getRptMonthOkRateExport,
  getCustomerList
} from "@/api/statement/qualityReport.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
export default {
	name: "FinalPassRateTendTotal",
	components: {
		NavBar,
		vTable,
		Echart,
		vForm,
	},
	data() {
		const colors = ["#5470C6", "#91CC75", "#EE6666"];
		var initRate = (rule, value, callback) => {
			if (!value) {
				callback();
			}
			let reg = /^(0(\.\d{1,2})?|([1-9]\d?)(\.\d{1,2})?|100(\.0{1,2})?)$/;
			if (reg.test(value)) {
				callback();
			}
			callback(new Error("请输入一个大于0小于等于100最多两位小数的数值"));
		};

		return {
      customerList:[],
			flag: false,
			rowData: {},
			selectRowData: [],
			BarOption: {
				color: colors,
				legend: {
					data: ["实际合格率", "目标合格率"],
					left: 10,
				},
				title: {
					text: "最终合格率不良总趋势",
					left: "center",
				},
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "cross",
					},
				},
				grid: {
					right: "20%",
				},
				toolbox: {},

				xAxis: [
					{
						type: "category",
						axisTick: {
							alignWithLabel: true,
						},
						// prettier-ignore
						data: [],
					},
				],
				yAxis: [
					{
						type: "value",
						name: "实际合格率",
						position: "left",
						alignTicks: true,
            min: 0,
            max: 100,
						axisLine: {
							show: true,
							lineStyle: {
								color: colors[0],
							},
						},
						axisLabel: {
							formatter: "{value} %",
						},
					},
          {
						type: "value",
						name: "目标合格率",
						position: "right",
						alignTicks: true,
            min: 0,
            max: 100,
						axisLine: {
							show: true,
							lineStyle: {
								color: colors[1],
							},
						},
						axisLabel: {
							formatter: "{value} %",
						},
					},
				],
				series: [
					{
						name: "实际合格率",
						type: "line",
						yAxisIndex: 0,
						data: [],
					},
          {
						name: "目标合格率",
						type: "line",
						yAxisIndex: 1,
						data: [],
					},
				],
			},
			listNavBarList: {
				title: "最终合格率不良列表",
				list: [
          {
						Tname: "导出",
						Tcode: "export",
					}
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
        height:400,
        showSummary:false,
				tableData: [],
				tabTitle: [
					{
						label: "月份",
						prop: "eleName",
					},
				],
        summaryObj: {
            summaryTitle: ["小计",'合计'],
            tableData: [],
          },
			},

			formOptions: {
				ref: "FinalPassRateTendTotal",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					// {
					// 	label: "客户",
					// 	prop: "customerCodeList",
					// 	type: "select",
					// 	clearable: true,
					// 	labelWidth: "80px",
					// 	multiple: true,
					// 	options: () => {
					// 		return this.customerList;
					// 	},
					// },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
					{ label: "入库时间", prop: "cdate", type: "monthrange", span: 8 },
				],
				data: {
					customerCodeList: [],
					partNo: "",
					innerProductNo: "",
					cdate: this.$getDefaultDateRange(365),
				},
			},
      rateModel: {
        rate:""
      },
      currentRate:100,
      showSetRateDialog:false,
      rule: {
        rate: [
          {
            validator: initRate,
            trigger: ["blur", "change"],
          },
        ],
        
      },
		};
	},
	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
        case "设置目标合格率":
					this.showSetRateDialog = true
          this.rateModel.rate = this.currentRate
					break;
        case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
    setRate() {
      this.currentRate = this.rateModel.rate
      this.BarOption.series[1].data = Array.from({ length: this.listTable.tabTitle.length-1 }, () => this.currentRate);
       this.$refs['rateForm'].validate((valid) => {
        if (valid) {
          this.showSetRateDialog = false
        }
       })
		},
		getCustomerList(){
      getCustomerList({}).then(res => {
        this.customerList = res.data.map(item=>{
          return {
            label: item.customerName,
            value: item.customerCode
          }
        })
      })
    },
		handleDownload() {
      let obj = {
				...this.formOptions.data,
        cdateStart: !this.formOptions.data.cdate ? null : this.getFirstDayOfMonth(this.formOptions.data.cdate[0]) || null,
				cdateEnd: !this.formOptions.data.cdate ? null : this.getFirstDayOfMonth(this.formOptions.data.cdate[1],1) || null,
			};
			getRptMonthOkRateExport(obj).then((res) => {
				console.log(res);
				this.$download("", "最终合格率月度总趋势", res);
			});
		},
		selectData(val) {
			this.rowData = _.cloneDeep(val);
		},
		checkData(arr) {
			this.selectRowData = _.cloneDeep(arr);
		},
		changeSize(val) {
			this.listTable.size = val;
			this.searchClick();
		},
		changePage(val) {
			this.listTable.count = val;
			this.searchClick(val);
		},
		async init() {
      this.getCustomerList()
			this.searchClick("1");
		},
		//获取月的第一天
		getFirstDayOfMonth(time,count = 0){
			const date = new Date(time);
			return new Date(date.getFullYear(), date.getMonth() + count, 1).getTime();
		},
		searchClick(val) {
			if (!val) this.listTable.count = 1;
			let obj = {
				...this.formOptions.data,
        cdateStart: !this.formOptions.data.cdate ? null : this.getFirstDayOfMonth(this.formOptions.data.cdate[0]) || null,
				cdateEnd: !this.formOptions.data.cdate ? null : this.getFirstDayOfMonth(this.formOptions.data.cdate[1],1) || null,
			};

			getRptMonthOkRate(obj).then((res) => {
        var tableData = [
          {
            eleName:'实际合格率（%）',
          },
          {
            eleName:'目标合格率（%）'
          },
          {
            eleName:'合格数'
          },
          {
            eleName:'不良数'
          },
          {
            eleName:'总数'
          },
        ];
        var tabTitle = [
					{
						label: "月份",
						prop: "eleName",
					},
				]
        res.data.forEach((item,index) => {
					tabTitle.push({
            label:item.yearMonth,
            prop:'prop' + index
          })
				});
        tableData.forEach((item1,index1) => {
          res.data.forEach((item,index) => {
            if(index1 == 0){
              item1[`prop${index}`] = parseFloat((item.okRate * 100).toFixed(2))
            }
            if(index1 == 1){
              item1[`prop${index}`] = parseFloat((item.targetRate * 100).toFixed(2))
            }
            if(index1 == 2){
              item1[`prop${index}`] = item.okQty
            }
            if(index1 == 3){
              item1[`prop${index}`] = item.ngQty

            }
            if(index1 == 4){
              item1[`prop${index}`] = item.totalQty
            }
				  });
        })
        this.listTable.tableData = tableData
        this.listTable.tabTitle = tabTitle
        this.BarOption.xAxis[0].data = res.data.map((item) => {
          return item.yearMonth
        })
        this.BarOption.series[1].data = res.data.map((item) => {
          return parseFloat((item.targetRate * 100).toFixed(2));
        })
        this.BarOption.series[0].data = res.data.map((item) => {
          return parseFloat((item.okRate * 100).toFixed(2));
        })
			});

		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
