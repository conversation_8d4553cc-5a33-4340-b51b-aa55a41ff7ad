<template>
  <el-dialog
		title="检验结果预览"
		width="90%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showPreviewInspectionDialog">
  <div style="overflow-x: scroll;">
    <section
      class="table-wrap com-page"
      style="width: max-content; margin: 20px auto"
    >
      <div class="fl row-end" style="width:100%">任务号:{{taskInfo.taskNo}}</div>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          发票号
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
         {{taskInfo.ticket}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          存货编码
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
         {{taskInfo.partNo}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          供应商
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
         {{taskInfo.supplierName}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          检查日期
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
         {{taskInfo.checkTime}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          规格
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: auto; line-height: 12px;font-size: 14px; flex-basis: 75%; flex-grow: 0; width: 75%">
         {{taskInfo.unit}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          检验人
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: auto; line-height: 12px;font-size: 14px; flex-basis: 75%; flex-grow: 0; width: 75%">
         {{taskInfo.checkUser}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          材质
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: auto; line-height: 12px;font-size: 14px; flex-basis: 75%; flex-grow: 0; width: 75%">
         {{taskInfo.mrmodel}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto" >
        <li style="display: flex; align-items: center; justify-content: center;  line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          确认人
        </li>
        <li style="display: flex; align-items: center; justify-content: center; line-height: 40px;font-size: 14px; flex-basis: 60%; flex-grow: 0; width: 60%">
         {{taskInfo.confirmUser}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center;  line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          COC
        </li>
        <li style="display: flex; align-items: center; justify-content: center; line-height: 40px;font-size: 14px; flex-basis: 60%; flex-grow: 0; width: 60%" >
         {{taskInfo.ifCoc|YESorNO}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center;  line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          合格证
        </li>
        <li style="display: flex; align-items: center; justify-content: center; line-height: 40px;font-size: 14px; flex-basis: 60%; flex-grow: 0; width: 60%" >
         {{taskInfo.ifCertificate|YESorNO}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center;  line-height: 40px;font-size: 14px; flex-basis: 25%; flex-grow: 0; width: 25%">
          检测报告
        </li>
        <li style="display: flex; align-items: center; justify-content: center; line-height: 40px;font-size: 14px; flex-basis: 60%; flex-grow: 0; width: 60%" >
         {{taskInfo.ifReport|YESorNO}}
        </li>
      </ul>
      <ul class="m-table-head">
        <li
          v-for="title in titles"
          :key="title.prop"
          :style="title.style + `height: 40px; line-height: 40px;width: 120px;`"
        >
          {{ title.label }}
        </li>
      </ul>

      <div class="m-table-body">
        <ul v-for="(item, ind) in contents" :key="ind" style="height: auto">
          <li
            v-for="title in titles"
            :key="title.prop"
            :style="
              title.style +
              `display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;flex-basis: 25%; flex-grow: 0; width: 25%`
            "
          >
            <span>{{ item[title.prop] }}</span>
          </li>
        </ul>
      </div>
    </section>
  </div>
  <div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit()">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom()">取 消</el-button>
		</div>
  </el-dialog>
</template>
<script>
import _ from "lodash";
import { formatYD } from "@/filters/index.js";
import {
	getFindFlatTaskById,getConfirmTaskById
} from "@/api/qam/incomingInspection.js";
export default {
  name: "PreviewInspection",
  filters: {
    YESorNO: function (value) {
      return value == "0" ? "是" : "否";
    }
  },
  props: {
    showPreviewInspectionDialog: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      taskInfo:{},//任务信息
      titles:[],
      contents:[],

    };
  },

  created() {
    getFindFlatTaskById({
      id: this.id,
    }).then((res) => { 
      this.taskInfo = res.data.task
      this.taskInfo.checkTime = formatYD(res.data.task.checkTime)
      for (const key in res.data.header) {
        let title = {}
        if (Object.prototype.hasOwnProperty.call(res.data.header, key)) {
          const element = res.data.header[key];
          title.prop = key
          title.label = element
        }
        this.titles.push(title)
      }
      this.contents = res.data.body
      console.log(this.titles,this.contents)
    });
  },
  mounted() {
  
  },

  methods: {
    resetFrom(form) {
		  this.$emit("update:showPreviewInspectionDialog", false);
	  },
    submit(){
      getConfirmTaskById({
        id: this.id
      }).then((res) => { 
        	this.$emit("submitHandler");
					this.$emit("update:showPreviewInspectionDialog", false);
      });
    }
  },
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner {
  height: 40px;
}

.table-wrap {
  width: 40%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    height: 60px;
    display: flex;
    justify-content: center;
    padding-right: 10px;
    padding-bottom: 10px;
    position: relative;
    .logo{
        position: absolute;
        top: 0px;
        left: 10px;
        width: 200px;
       
      }
    .center {
      font-size: 24px;
      font-weight: bold;
      display: flex;
      flex-direction: column;
      text-align: center;
      vertical-align:middle;
      position: relative;
      
    }
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #000;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    position: relative;

    > li {
      flex: 1;
      border-left: 1px solid #000;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
      height: 30px;
      line-height: 38px;
    }
    &.border-none-top {
      border-top: 0 none;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #000;
      > li {
        flex: 1;
        border-right: 1px solid #000;
        &:first-child {
          border-left: 1px solid #000;
        }
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.color-red {
  color: red;
}
.sign-wrap {
  position: absolute;
  bottom: 2px;
  right: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-right: 40px;
  font-size: 12px;
  width: auto;
  
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
  }
  // page-break-after:always;
  .com-page {
    page-break-after: always;
  }
  .table-wrap {
    margin-top: 0;
    
  }
  .print-display-none {
    display: none;
  }
}
</style>
