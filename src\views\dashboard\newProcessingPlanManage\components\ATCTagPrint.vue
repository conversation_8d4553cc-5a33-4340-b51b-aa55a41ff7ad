<template>
  <div class="printF-wrap">
    <nav class="print-display-none">
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <div id="printTest" style="overflow: hidden !important">
      <div v-for="(item, index) in qrcodeData" :key="index" class="print-height">
        <div class="qrcode-no-pos">
          <div class="count-wrapper">
            <div class="count-item" style="align-items: center">
              <img class="sn-image" :src="item.image" alt="" />
              <div class="big-font">SN</div>
            </div>
            <div class="count-item">
              <div class="big-font">{{ `PN:${item.innerProductNo}` }}</div>
              <div class="big-font">{{ `SN:${item.serialNo}` }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import JsBarcode from "jsbarcode";
import { formatYD } from "@/filters/index.js";
import { echoQrcode } from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  filters: {
    formatYD,
  },
  data() {
    return {
      localPrintConfig: {
        popTitle: "&nbsp;",
      },
      qrcodeData: [],
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
    },
  },
  methods: {
    async echoQrcode() {
      try {
        const originData = JSON.parse(sessionStorage.getItem("batchPrintData") || "[]");
        console.log(`output->originData`, originData);
        const qrList = originData.map(({ serialNo }) => serialNo);
        const { data } = await echoQrcode({ qrList, width: 200, height: 200 });
        data.forEach(({ image }, index) => {
          originData[index].image = "data:image/jpg;base64," + image;
        });
        this.qrcodeData = originData;
      } catch (e) {
        console.log(e);
      }
    },
  },
  mounted() {
    this.echoQrcode();
  },
};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .mb-10 {
    margin-bottom: 10px;
  }
}
.print-display-none {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  padding-top: 10px;
}
.print-height {
  background-color: #fff;
  width: 312px;
  page-break-after: always;
  overflow: hidden !important;
  // font-weight: 600;
  font-family: Arial, Helvetica, sans-serif;
}
.qrcode-no-pos {
  font-size: 14px;
  line-height: 22px;
  padding: 20px;
  position: relative;
  .count-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .count-item {
      display: flex;
      flex-direction: column;
      .big-font {
        font-size: 20px;
        margin-bottom: 4px;
        line-height: 28px;
        max-width: 190px;
        overflow-wrap: break-word;
      }
      .sn-image {
        width: 60px;
        height: 60px;
      }
    }
  }
  .amec-logo {
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    padding-right: 30px;
    padding-left: 40px;
    img {
      height: 100%;
    }
  }
}
@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    text-shadow: none !important; /* 禁用文字阴影 */
  }
  .print-height {
    width: 100%;
    page-break-after: always;
    overflow: hidden !important;
  }
  .qrcode-no-pos {
    height: 28mm;
    width: 78mm;
    font-family: Arial;
    font-size: 3mm;
    line-height: 5mm !important;
    padding: 4mm;
    position: relative;
    font-family: Arial, Helvetica, sans-serif;
    .count-wrapper {
      display: flex;
      flex-direction: row !important;
      justify-content: space-between !important;
      width: 100%;
      .count-item {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        .big-font {
          font-size: 5mm;
          line-height: 6mm;
          max-width: 50mm;
          margin-bottom: 1mm;
        }
        .sn-image {
          width: 14mm;
          height: 14mm;
        }
      }
    }
    .amec-logo {
      height: 20mm;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: 4mm;
      width: 100%;
      padding-left: 3mm;
      padding-right: 3mm;
      img {
        height: 100%;
      }
    }
  }
}
</style>
