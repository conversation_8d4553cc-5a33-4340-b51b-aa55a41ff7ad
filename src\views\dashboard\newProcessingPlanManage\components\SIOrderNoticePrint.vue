<template>
  <div
    id="printTableContainer"
    style="width: 100%; overflow: hidden !important"
  >
    <nav class="print-display-none">
      <div style="margin-right: 10px">
        每页条数
        <el-input-number
          class="number-height"
          v-model="pageSize"
          :step="1"
          :precision="0"
        />
      </div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section
      v-for="(dataItem, index) in oneData"
      :key="index"
      class="table-wrap com-page"
      style="width: 100%; margin: 20px auto"
    >
      <div class="m-table-title">
        <div class="center">
          <header style="font-size:24px">
            SI订单生产通知单</header>
        </div>
      </div>
      <ul class="m-table-head basic-infor" style="height: auto;padding:5px">
        <!-- <svg :id="'barCode' + dataItem.batchNumber"></svg> -->
        <li style="display: flex; align-items: center; justify-content: flex-start; height: 80px; line-height: 80px;font-size: 16px; flex-basis: 100%; flex-grow: 0; width: 100%;border:none">
            <img v-if="dataItem.image" class="qrimage" :src="dataItem.image"/>
        </li>
        
      </ul>
    
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          制造番号
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.makeNo}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          生产批号
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.batchNumber}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          物料编码
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.partNo}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          产品名称
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.productName}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          产品图号
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.innerProductNo}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          单位
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.workOrderUnit}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          订单数量
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.orderQty}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          本批数量
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.quantityInt}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          完工日期
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.workOrderPlanEndDate}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          入库仓库
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.warehouseName}}
         <!-- 产成品仓库 -->
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          客户名称
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.workOrderCustomerName}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          客户订单号
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 18px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
         {{dataItem.workOrderCustomerOrder}}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          备注
        </li>
        <li style="display: flex; align-items: center;padding:10px;height: auto; line-height: 12px;font-size: 16px; flex-basis: 80%; flex-grow: 0; width: 80%">
          {{dataItem.workOrderRemark}}
        </li>
      </ul>
      <ul class="m-table-head " style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          制单人
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
          {{dataItem.createdBy}}
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 20%; flex-grow: 0; width: 20%">
          打印人
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 16px; flex-basis: 30%; flex-grow: 0; width: 30%">
        {{userName}}
        </li>
      </ul>
    </section>
  </div>
</template>
<script>
import JsBarcode from 'jsbarcode'
import { Storage } from "@/utils/storage.js";
import { formatYD } from "@/filters/index.js";
import {
  echoQrcode
} from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
  name: "SIOrderNoticePrint",
  data() {
    return {
      getConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
      },
      userName:"",
      oneData:[],
      pageSize: 30,
    };
  },
  computed: {
    echoTableList() {
      const a = this.oneData || [] 
      const res = [];
      while (a.length > this.pageSize) {
        res.push(a.splice(0, this.pageSize));
      }

      if (a.length !== 0) {
        res.push(a);
      }else{
        res.push([])
      }
  

      return res;
    },
  },
  async mounted() {
    try {
      this.userName = Storage.getItem("username");
      const originData = JSON.parse(sessionStorage.getItem("siBatchList"))
      const qrList = originData.map(({ batchNumber }) => batchNumber)
      const { data } = await echoQrcode({ qrList, width: 200,  height: 200 })
      data.forEach(({ image }, index) => {
        originData[index].image = 'data:image/jpg;base64,' + image
      })
      this.oneData = originData
    } catch (e) {
      console.log(e)
    }
    
    this.$nextTick(()=>{
        this.resizeBarCode();
    })
  },

  methods: {
  async resizeBarCode() {
    
      console.log(this.oneData)
      // this.oneData.forEach(({ batchNumber }, index) => {
      //   JsBarcode("#barCode"+ batchNumber, batchNumber, {
      //     width: 1.4,
      //     height: 40,
      //     format: "CODE128", //选择要使用的条形码类型
      //     margin: 0, //设置条形码周围的空白边距
      //     marginBottom: 0, //设置条形码周围的空白边距
      //     marginTop: 0, //设置条形码周围的空白边距
      //     background: "#FFF",
      //     displayValue: false, //是否在条形码下方显示文字
      //   });
      // });
    },
  },
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner {
  height: 40px;
}

.table-wrap {
  width: 40%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    height: 60px;
    display: flex;
    justify-content: center;
    padding-right: 10px;
    padding-bottom: 10px;
    .center {
      font-size: 20px;
      font-weight: bold;
      display: flex;
      text-align: center;
      vertical-align:middle;
    }
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #000;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    position: relative;

    > li {
      flex: 1;
      border-left: 1px solid #000;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
      height: 30px;
      line-height: 38px;
    }
    &.border-none-top {
      border-top: 0 none;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #000;
      > li {
        flex: 1;
        border-right: 1px solid #000;
        &:first-child {
          border-left: 1px solid #000;
        }
      }
    }
  }
}
.qrimage{
  right: 10px;
  top: 10px;
  width: 80px;
  height: 80px;
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.color-red {
  color: red;
}
.sign-wrap {
  position: absolute;
  bottom: 2px;
  right: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-right: 40px;
  font-size: 9px;
  width: auto;
  
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    .basic-infor {
      font-size: 16px;
      
    }
  }
  .m-table-head {
    display: flex;
    border: 1px solid #000;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    position: relative;

    > li {
      flex: 1;
      border-left: 1px solid #000;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
      height: 30px;
      line-height: 38px;
    }
    &.border-none-top {
      border-top: 0 none;
    }
  }
  .qrimage{
  width: 80px;
  height: 80px;
}
  // page-break-after:always;
  .com-page {
    page-break-after: always;
  }
  .table-wrap {
    margin-top: 0;
    
  }
  .print-display-none {
    display: none;
  }
}
</style>
