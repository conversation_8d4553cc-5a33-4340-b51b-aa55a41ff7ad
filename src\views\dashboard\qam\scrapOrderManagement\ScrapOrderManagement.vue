<template>
	<!-- 报废单管理 -->
	<div class="scrapOrderManage">
		<vForm ref="porFormRef" :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" style="width: 98%">
				<NavBar :nav-bar-list="scrapNavBarList" @handleClick="scrapNavClick">
					<template #right>
                        <div class="default-section-scan">
                            <ScanCode
                                v-model="qrCode"
                                :lineHeight="25"
                                :markTextTop="0"
                                :first-focus="false"
                                @enter="qrCodeEnter"
                                placeholder="请扫描批次码" />
                        </div>
                    </template>
                </NavBar>
				<vTable
					refName="scrapTable"
					:table="scrapTable"
                    :needEcho="false"
					@checkData="selectScrapSingle"
                    @changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" 
                >
					<div slot="scrapFile" slot-scope="{ row }">
						<span
							style="color: #1890ff"
							v-if="row.scrapBillNo"
							@click="checkScrapFile(row)"
							class="el-icon-paperclip"
						></span>
					</div>
					<div slot="billPicture" slot-scope="{ row }">
						<span
							style="color: #1890ff"
							v-if="row.billPicture"
							@click="checkScrapAttach(row)"
							class="el-icon-paperclip"
						></span>
					</div>
				</vTable>
                <NavBar class="mt22" :nav-bar-list="scrapBatchNavBarList"/>
                <vTable
                    refName="scrapBatchTable"
                    :table="scrapBatchTable"
                    checked-key="id" 
                />
			</section>
		</div>
        <!-- 导入文件 -->
		<FileUploadDialog
			:visible.sync="importMarkFlag"
			:limit="1"
			title="上传附件"
			@submit="uploadAttach"
		/>
		<!-- 修改报废单 -->
		<template v-if="showAddScrapDialog">
			<addScrapDialog
				:isEdit="true"
				:showAddScrapDialog.sync="showAddScrapDialog"
				:currentScrapRow="currentScrapRow"
				@addScrapHandle="searchClick()" />
		</template>
	</div>
</template>
<script>

import { 
	getScrapBillListApi,
	getScrapBillByIdApi,
	startScrapBillApi,
	revokeBillApi,
	exportScrapBillApi,
	uploadScrapApi,
	getScrapBillByBatchApi
} from "@/api/qam/scrapOrderManagement.js";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import addScrapDialog from "../scrapManagement/components/addScrapDialog.vue";

export default {
	name: "ScrapOrderManagement",
	components: {
		NavBar,
		vTable,
		vForm,
        ScanCode,
        FileUploadDialog,
		addScrapDialog
	},
	data() {
		return {
			formOptions: {
				ref: "scrapOrderRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{
						label: "审批状态",
						prop: "auditStatus",
						type: "select",
						clearable: true,
						labelWidth: "96px",
						options: () => this.auditStatusOption
					},
					{ label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
					{ label: "报废单号", prop: "scrapBillNo", type: "input", clearable: true },
					{ label: "报废发起人", prop: "createdBy", type: "input", labelWidth: "96px", clearable: true },
					{ label: "报废发起时间", prop: "time", type: "datetimerange", span: 8, labelWidth: "110px" },
				],
				data: {
					auditStatus: "",
					innerProductNo: "",
					partNo: "",
					scrapBillNo: "",
					time: this.$getDefaultDateRange(365),
				},
			},
            auditStatusOption: [
                { dictCode: "0", dictCodeValue: "审批中" },
                { dictCode: "1", dictCodeValue: "审批通过" },
                { dictCode: "2", dictCodeValue: "审批驳回" },
				{ dictCode: "3", dictCodeValue: "撤回" },
				{ dictCode: "4", dictCodeValue: "草稿" },
            ],
			scrapStatusOption: [
				{ dictCode: "0", dictCodeValue: "待报废" },
                { dictCode: "1", dictCodeValue: "报废中" },
                { dictCode: "2", dictCodeValue: "已报废" },
				{ dictCode: "3", dictCodeValue: "取消" },
			],
            scrapNavBarList: {
				title: "报废单详情",
				list: [
					{
						Tname: "修改",
						Tcode: "update",
					},
					{
						Tname: "发起",
						Tcode: "initiate",
					},
					{
						Tname: "撤回",
						Tcode: "withdraw",
					},
                    {
						Tname: "打印",
						Tcode: "print",
					},
					{
						Tname: "上传附件",
						Tcode: "upload",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
            scrapTable: {
                count: 1,
				size: 10,
				maxHeight: "320",
				tableData: [],
				tabTitle: [
                    { label: "报废单号", width: "180", prop: "scrapBillNo" },
					{ label: "物料编码", width: "180", prop: "partNo" },
					{ label: "产品图号", width: "150", prop: "innerProductNo" },
                    { label: "报废数量",  width: "100", prop: "scrapQty" },
					{ 
						label: "创建时间", 
						width: "180", 
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						}
					},
                    { label: "报废原因", width: "180", prop: "reason" },
					{ label: "报废属性", width: "100", prop: "scrapAttr" },
                    { label: "报废发起人", width: "150", prop: "createdBy" },
                    { 
						label: "报废发起时间",  
						width: "180", 
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						}
					},
					{ label: "查看报废单", width: "100", prop: "scrapFile", slot: true },
					{ label: "查看附件", width: "100", prop: "billPicture", slot: true },
                    { 
						label: "审批状态", 
						width: "100", 
						prop: "auditStatus",
						render: (row) => this.$checkType(this.auditStatusOption, row.auditStatus),
					 },
                    { label: "当前节点",  width: "120", prop: "currentNode" },
					{ label: "审批人",  width: "120", prop: "auditPerson" },
					{ label: "备注", width: "180", prop: "remark" },
				],
			},
			scrapBatchNavBarList: {
				title: "批次详情",
			},
            scrapBatchTable: {
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "批次号", prop: "batchNumber" },
					{ label: "工单", prop: "workOrderCode",
						render: (row) => row.orderBatch?.workOrderCode
					},
					{ label: "数量", width: "100", prop: "qty" },
					{ label: "创建时间",  width: "180", prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						}
					},
					{ label: "NG码", width: "150", prop: "ngCode" },
					{ 
						label: "报废状态", 
						width: "150", 
						prop: "status",
						render: (row) => this.$checkType(this.scrapStatusOption, row.status), 
					},
					// { label: "报废原因", prop: "" },
                    // { label: "报废属性", width: "100", prop: "" },
					{ label: "责任人", width: "150", prop: "responsiblePerson" },
					{ label: "报废工序",  width: "150", prop: "scrapProcessName" },
					{ label: "报废判定人", width: "150", prop: "scrapApplyPerson" },
					{ label: "报废判定时间", width: "180", prop: "scrapApplyDate",
						render: (row) => {
							return formatYS(row.scrapApplyDate);
						}
					},
				],
			},		
			currentScrapRow: {}, // 选中的报废单信息 
            qrCode: "",
            importMarkFlag: false,
			showAddScrapDialog: false,
		}
	},
	created() {
		this.searchClick()
	},
	methods: {
		// 查看申请单
        checkScrapFile(row){
            window.open(location.href.split("/#/")[0] + "/#/qam/scrapInfoPrint?id=" + row.scrapBillNo);
        },
		// 查看附件
		checkScrapAttach(row){
			window.open(this.$getFtpPath(row.billPicture))
        },
		// 查询报废单列表 
		searchClick(val) {
            if (val) {
				this.scrapTable.count = val
			}
            const param = {
				data: {
					...this.formOptions.data,
					createdTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
					createdTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
				},
				page: {
					pageNumber: this.scrapTable.count,
					pageSize: this.scrapTable.size,
				},
			};
			delete param.data.time
			getScrapBillListApi(param).then((res) => {
				this.scrapTable.tableData = res.data;
				this.scrapTable.total = res.page.total;
				this.scrapTable.count = res.page.pageNumber;
				this.scrapTable.size = res.page.pageSize;
                this.currentScrapRow = {} 
                this.scrapBatchTable.tableData = [];
			});
		},
        changeSize(val) {
			this.scrapTable.size = val;
			this.searchClick(1);
		},
		changePages(val) {
			this.scrapTable.count = val;
			this.searchClick(val);
		},
		// 重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		// 选中报废单
		selectScrapSingle(val) {
			if (JSON.stringify(val) != "{}") {
				getScrapBillByIdApi({ id: val.id }).then((res) => {
					this.currentScrapRow = res.data
					this.scrapBatchTable.tableData = _.cloneDeep(this.currentScrapRow.scrapList)
				});
			} else {
				this.currentScrapRow = {}
				this.scrapBatchTable.tableData = []
			}
		},
		// 报废单详情右侧按钮
		scrapNavClick(val) {
			switch (val) {
				case "修改":
                    this.operateScrap("update");
					break;
				case "发起":
                    this.operateScrap("initiate");
					break;
				case "撤回":
					this.operateScrap("withdraw");
					break;
                case "打印":
					this.operateScrap("print");
					break;
				case "上传附件":
					this.operateScrap("upload");
					break;
				case "导出":
					this.exportScrap();
					break;
				default:
					return;
			}
		},
		// 操作报废单
		operateScrap(operateFlag) {
            if (!this.currentScrapRow.id) {
                this.$showWarn("请选择要操作的报废单"); 
                return;
            }
            switch (operateFlag) {
				case "update":
					if (this.currentScrapRow.auditStatus !== '2' && this.currentScrapRow.auditStatus !== '3' && this.currentScrapRow.auditStatus !== '4') {
						this.$showWarn("不能修改审批中与审批通过的报废单"); 
                		return;
					}
					this.showAddScrapDialog = true
					break;
                case "initiate":
					this.$confirm(`确认对当前选中报废单发起报废吗?` , "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						startScrapBillApi(this.currentScrapRow).then((res) => {
							this.$responsePrecedenceMsg(res).then(() => {
								this.searchClick();
							})
						})
					}).catch(() => {})
					break;
				case "withdraw":
					this.$confirm(`确认撤回当前选中报废单的报废申请吗?` , "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						revokeBillApi(this.currentScrapRow).then((res) => {
							this.$responsePrecedenceMsg(res).then(() => {
								this.searchClick();
							})
						})
					}).catch(() => {})
					break;
                case "print":
					this.checkScrapFile(this.currentScrapRow)
					break;
				case "upload":
                    this.importMarkFlag = true
					break;
				default:
					return;
			}
		},
        // 上传附件
        uploadAttach(fileData) {
            if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
			const formData = new FormData();
			formData.append("file", fileData.fileList[0]?.raw);
			formData.append("id", this.currentScrapRow.id);
			uploadScrapApi(formData).then((res) => {
				this.$responsePrecedenceMsg(res).then(() => {
					this.searchClick();
					this.importMarkFlag = false;
				});
			});
        },
        // 导出报废单
        exportScrap() {
            const param = {
				...this.formOptions.data,
				createdTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
				createdTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
			};
            exportScrapBillApi(param).then((res) => { 
				if (!res) {
					return;
				}
				this.$download("", "报废单清单", res);
			});
        },
        qrCodeEnter() {
			getScrapBillByBatchApi({
				batchNumber: this.qrCode
			}).then(res => {
				console.log(res.data)
				if (!res.data) {
					this.$showWarn("未查询到有该批次号对应的报废单");
					return;
				}
				this.scrapTable.tableData = res.data;
				this.scrapTable.total = 0;
				this.scrapBatchTable.tableData = res.data[0].scrapList.filter(item => item.batchNumber === this.qrCode)
			})
		},
	}
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}

.default-section-scan {
	::v-deep .el-input__inner {
		height: 26px;
		line-height: 26px;
	}
}
</style>