import request from "@/config/request.js";

// 分页查询报废单列表
export function getScrapBillListApi(data) {
    return request({
        url: "/ppScrapBill/getPage",
        method: "post",
        data,
    });
}

// 根据id查询报废单详情
export function getScrapBillByIdApi(params) {
    return request({
        url: "/ppScrapBill/getById",
        method: "get",
        params,
    });
}

// 根据批次号查询报废单
export function getScrapBillByBatchApi(params) {
    return request({
        url: "/ppScrapBill/getListByBatchNumber",
        method: "get",
        params,
    });
}

// 保存报废单
export function saveScrapBillApi(data) {
    return request({
        url: "/ppScrapBill/save",
        method: "post",
        data,
    });
}

// 发起报废申请单
export function startScrapBillApi(data) {
    return request({
        url: "/ppScrapBill/startBill",
        method: "post",
        data,
    });
}

// 撤销报废申请单
export function revokeBillApi(data) {
    return request({
        url: "/ppScrapBill/revokeBill",
        method: "post",
        data,
    });
}

// 报废单导出
export function exportScrapBillApi(data) {
    return request({
        url: "/ppScrapBill/export",
        method: "post",
        data,
        responseType: "blob",
        timeout:1800000
    });
}

// 上传附件
export function uploadScrapApi(data) {
    return request({
        url: "/ppScrapBill/upload",
        method: "post",
        data,
    });
}


