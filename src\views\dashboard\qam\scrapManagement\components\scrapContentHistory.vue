<!--
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2024-10-15 09:21:50
-->
<template>
	<el-dialog
		:title="dialogTitle"
		width="70%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showScrapContentListDialog"
		append-to-body>
		<div style="max-height: 550px; overflow: hidden; overflow-y: scroll">
			<!-- <el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-4" label="报废属性" label-width="80px" prop="scrapInfo">
						<el-select v-model="ruleFrom.scrapInfo" placeholder="请选择报废属性" clearable filterable>
							<el-option
								v-for="item in scrapInfoDict"
								:key="item.dictCode"
								:label="item.dictCodeValue"
								:value="item.dictCode"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item class="el-col el-col-4" label="NG码" label-width="80px" prop="ng">
						<el-select v-model="ruleFrom.ng" placeholder="请选择NG码" clearable filterable>
							<el-option
								v-for="item in ngDict"
								:key="item.dictCode"
								:label="item.dictCodeValue"
								:value="item.dictCode"></el-option>
						</el-select>
					</el-form-item>
				</el-row>
			</el-form> -->
			<vTable
				:table="table"
				@checkData="checkRow"
				@getRowData="selectRows"
				checkedKey="scrapContent" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitMark">确 定</el-button>
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import _ from "lodash";
export default {
	name: "scrapContentHistory",
	components: {
		vTable,
		NavBar,
	},
	props: {
		showScrapContentListDialog: {
			type: Boolean,
			default: false,
		},
		// 审批节点
		approvalNode: {
			type: String,
			default: ""
		},
		preventionHis: {
			type: Array,
			default: () => []
		},
		contentHis: {
			type: Array,
			default: () => []
		},
	},
	data() {
		return {
			rowData: {},
			rowDataArr: [],
			table: {
				check: true,
				tableData: [],
				tabTitle: [
					// { label: "报废属性", prop: "scrapInfo" },
					// { label: "NG码", prop: "ng" },
					{ label: '报废内容', prop: "scrapContent" },
					{ label: "记录人", prop: "createdBy" },
				],
			},
			dialogTitle: '选择历史内容',
			ruleFrom: {},
			// tableFilter: (item) => true,
      		// scrapInfoDict: [],
      		// ngDict: []
		};
	},
	created() {
		if (this.approvalNode === '预防对策') {
			this.dialogTitle = '选择历史政策'
			this.table.tabTitle[0].label = '报废政策'
			this.table.tableData = this.preventionHis.map(item => {
				return {
					scrapContent: item.prevention,
					createdBy: item.createdBy
				}
			})
		} else {
			this.dialogTitle = '选择历史内容'
			this.table.tabTitle[0].label = '报废内容'
			this.table.tableData = this.contentHis.map(item => {
				return {
					scrapContent: item.scrapContent,
					createdBy: item.createdBy
				}
			})
		}
		// this.sift()
	},
	methods: {
		checkRow(val) {
			this.rowData = val;
		},
		selectRows(val) {
			this.rowDataArr = _.cloneDeep(val);
		},
		closeMark() {
			this.$emit("update:showScrapContentListDialog", false);
		},
		submitMark() {
			if (this.rowDataArr.length) {
				this.$emit("selectRow", this.rowDataArr);
				this.closeMark()
			} else {
				this.$showWarn("请先勾选数据");
				return false;
			}
		},
		//设置筛选批次条件
		// sift() {
		// 	this.tableFilter = (item) => {
		// 		let { scrapInfo, ng} = this.ruleFrom;
		// 		if (!scrapInfo && !ng ) {
		// 			return true;
		// 		}
		// 		if (scrapInfo && scrapInfo != item.scrapInfo) {
		// 			return false;
		// 		}
		// 		if (ng && ng != item.ng) {
		// 			return false;
		// 		}
		// 		return true;
		// 	};
		// },
	},
};
</script>
