<template>
  <el-dialog
    :title="dialogTitle"
    width="70%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddScrapDialog"
  >
    <div class="mt10 flex1">
      <el-form ref="proPFrom1" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom" :rules="addScrapRule">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-7" label="发生时间" label-width="80px" prop="happenTime">
            <el-date-picker
              :disabled="approvalNode != ''"
              v-model="ruleFrom.happenTime"
              clearable
              type="datetime"
              value-format="timestamp"
              placeholder="选择发生时间"
            />
          </el-form-item>
          <el-form-item class="el-col el-col-7" label="报废属性" label-width="80px" prop="scrapAttr">
            <el-select
              v-model="ruleFrom.scrapAttr"
              placeholder="请选择报废属性 "
              clearable
              filterable
              :disabled="approvalNode != ''"
            >
              <el-option
                v-for="item in scrapAttrDict"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>

        <el-row>
          <el-form-item label="不良示意图" prop="scrapImg">
            <div class="row">
              <el-image v-if="imageUrl" class="ex-image" :src="imageUrl" :preview-src-list="[imageUrl]"> </el-image>
              <el-upload
                class="avatar-uploader"
                accept=".png,.jpg,.jpeg,.svg,JPG,JPEG"
                :auto-upload="false"
                action=""
                :show-file-list="false"
                :on-change="changeAddImg"
              >
                <el-button v-if="!imageUrl && approvalNode == ''" class="noShadow blue-btn" size="small" type="primary"
                  >上传图片</el-button
                >
                <el-button
                  v-if="imageUrl && approvalNode == ''"
                  class="noShadow blue-btn ml7"
                  size="small"
                  type="primary"
                  >重新上传</el-button
                >
              </el-upload>
            </div>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item class="el-col el-col-21" label="报废内容" label-width="80px" prop="scrapContent">
            <div class="fl row-start" style="width: 100%">
              <el-input
                rows="5"
                maxlength="300"
                v-model="ruleFrom.scrapContent"
                clearable
                :disabled="approvalNode != ''"
                placeholder="请输入报废内容"
                type="textarea"
              ></el-input>
              <div style="margin-left: 10px">
                <el-button
                  class="noShadow blue-btn"
                  type="primary"
                  @click="openScrapHistory('历史内容')"
                  v-if="approvalNode == ''"
                >
                  历史内容
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  style="margin-left: 0px"
                  v-if="approvalNode == ''"
                  @click="saveHistory"
                  >保存内容</el-button
                >
              </div>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="childNavBarList" @handleClick="childNavClick">
        <template #right>
          <ScanCode
            :disabled="approvalNode != ''"
            style="margin-left: 32px"
            v-model="qrCode"
            :lineHeight="25"
            :markTextTop="0"
            :first-focus="false"
            @enter="qrCodeEnter"
            placeholder="批次扫描框"
          />
        </template>
      </NavBar>
      <vTable
        refName="childTable"
        :table="childTable"
        @getRowData="selectScrapRows"
        @checkData="handleRowClick"
        checkedKey="id"
      />
      <!-- <NavBar :nav-bar-list="sizeNavBarList" @handleClick="sizeNavClick"></NavBar>
      <vTable :key="sizeTableKey" refName="sizeTable" :table="sizeTable" @getRowData="selectSizeRows" checkedKey="id" /> -->
      <el-form
        ref="proPFrom2"
        class="demo-ruleForm"
        @submit.native.prevent
        :model="approvalRuleFrom"
        :rules="approvalRule"
      >
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-7" label="发生部门/班组" label-width="120px" prop="occurDept">
            <el-select
              v-model="approvalRuleFrom.occurDept"
              placeholder="请选择发生部门/班组"
              clearable
              filterable
              :disabled="approvalNode == '预防对策'"
            >
              <el-option
                v-for="item in scrapOccurDeptOption"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-7" label="原因分类" label-width="120px" prop="reasonType">
            <el-select
              v-model="approvalRuleFrom.reasonType"
              placeholder="请选择原因类别"
              clearable
              filterable
              multiple
              :disabled="approvalNode == '预防对策'"
            >
              <el-option
                v-for="item in reasonTypeDict"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item class="el-col el-col-21" label="原因分析" label-width="120px" prop="reason">
            <div class="fl row-start" style="width: 100%">
              <el-input
                rows="5"
                v-model="approvalRuleFrom.reason"
                maxlength="300"
                show-word-limit
                clearable
                :disabled="approvalNode == '预防对策'"
                placeholder="请输入原因分析"
                type="textarea"
              />
            </div>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item class="el-col el-col-21" label="防止对策" label-width="120px" prop="prevention">
            <div class="fl row-start" style="width: 100%">
              <el-input
                rows="5"
                v-model="approvalRuleFrom.prevention"
                maxlength="300"
                show-word-limit
                clearable
                placeholder="请输入防止对策"
                :disabled="approvalNode == '预防对策'"
                type="textarea"
              ></el-input>
              <div style="margin-left: 10px">
                <el-button class="noShadow blue-btn" type="primary" @click="openScrapHistory('历史对策')">
                  历史对策
                </el-button>
                <el-button class="noShadow red-btn" style="margin-left: 0px" @click="savePrevention"
                  >保存对策</el-button
                >
              </div>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button v-if="!isEdit" class="noShadow blue-btn" type="primary" @click="saveScrap">暂存</el-button>
      <el-button class="noShadow blue-btn" type="primary" @click="submitScrap('proPFrom1','proPFrom2')">{{ submitBtn }}</el-button>
      <el-button class="noShadow red-btn" @click="closeAddScrap">取 消</el-button>
    </div>
    <!-- 选择历史内容/历史对策 -->
    <template v-if="showScrapContentListDialog">
      <scrapContentHistory
        :showScrapContentListDialog.sync="showScrapContentListDialog"
        :approvalNode="approvalNode"
        :preventionHis="preventionHis"
        :contentHis="contentHis"
        @selectRow="handleSelect"
      />
    </template>
    <template v-if="showSizeListDialog">
      <sizeList
        :showSizeListDialog.sync="showSizeListDialog"
        :addedSizeList="sizeTable.tableData"
        :batchNumber="currentOperateChild.batchNumber"
        @selectRow="selectSizesHandle"
      />
    </template>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp } from "@/filters/index.js";
import scrapContentHistory from "./scrapContentHistory.vue";
import sizeList from "./sizeList.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import { searchDD } from "@/api/api.js";
import { getStartScrapBill, getUploadNgPic, getScanOneBatchScrap ,selectOrganizationDepartment,getSaveScrapBill} from "@/api/qam/scrapManagement.js";
import { saveScrapBillApi } from "@/api/qam/scrapOrderManagement.js";
import { findRepairList } from "@/api/courseOfWorking/InboundOutbound";
import {
  auditFillPreventionApi,
  auditFillReasonApi,
  getAttributesApi,
  getPreventionHisApi,
  getReasonHisApi,
  getContentHisApi,
  savePreventionApi,
} from "@/api/qam/scrapApproval.js";
export default {
  components: {
    NavBar,
    vTable,
    ScanCode,
    scrapContentHistory,
    sizeList,
  },
  props: {
    showAddScrapDialog: {
      type: Boolean,
      default: false,
    },
    scrapList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 是否是报废单管理页面点击修改按钮打开
    isEdit: {
      type: Boolean,
      default: false,
    },
    // 要修改的报废单信息
    currentScrapRow: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 审批页面填写按钮跳转打开时的节点
    approvalNode: {
      type: String,
      default: "",
    },
  },
  created() {
    this.init();
  },
  data() {
    return {
      addScrapRule: {
        scrapContent: [{ required: true, message: "请输入报废内容", trigger: "blur" }],
        happenTime: [{ required: true, message: "请选择发生时间", trigger: "blur" }],
        scrapImg: [{ required: true, message: "请上传不良示意图", trigger: "blur" }],
      },
      scrapRowParams: {},
      showSizeListDialog: false,
      showScrapContentListDialog: false,
      scrapAttrDict: [],
      reasonTypeDict: [],
      scrapOccurDeptOption: [],
      childNavBarList: {
        title: "报废批次列表",
        list: [
          {
            Tname: "移除",
            Tcode: "export",
          },
        ],
      },
      sizeNavBarList: {
        title: "添加不良数据",
        list: [
          {
            Tname: "选择不合格尺寸",
            Tcode: "export",
          },
          {
            Tname: "移除",
            Tcode: "export",
          },
        ],
      },
      childTable: {
        check: true,
        maxHeight: 320,
        tableData: [],
        tabTitle: [
          { label: "批次号", prop: "batchNumber" },
          { label: "产品名称", prop: "productName" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "报废工序名称", prop: "scrapProcessName" },
          { label: "NG代码", prop: "ngCode" },
          { label: "NG描述", prop: "ngName" },
          { label: "数量", prop: "qty" },
          { label: "客户名称", prop: "customerName" },
          { label: "刻字号", prop: "letteringNo" },
          { label: "责任人", prop: "responsiblePerson" },
          { label: "责任部门", prop: "responsibleDept" },
        ],
      },
      sizeTable: {
        check: true,
        maxHeight: "220",
        tableData: [],
        tabTitle: [
          { label: "批次号", prop: "batchNumber" },
          { label: "尺寸序列", width: "180", prop: "dimensionNo" },
          { label: "尺寸名称", prop: "dimensionName" },
          { label: "单位", prop: "unit" },
          {
            label: "特征",
            prop: "feature",
          },
          { label: "标称值", prop: "dimensionValue" },
          { label: "上差", prop: "upperTolerance" },
          { label: "下差", prop: "lowerTolerance" },
          { label: "实测值", prop: "measureValue" },
          { label: "超差", prop: "deviationValue" },
        ],
      },
      currentOperateChild: {},
      currentChildIndex: 0,
      scrapRows: [], //多选报废批次
      sizeRows: [], //多选批次
      qrCode: "",
      ruleFrom: {
        productName: "",
        happenTime: Date.now(),
        innerProductNo: "",
        scrapAttr: "",
        scrapContent: "",
        scrapImg: "",
      },
      sizeTableKey: "",
      imageUrl: "",
      originImgUrl: "",
      dialogTitle: "创建报废单",
      approvalRuleFrom: {
        reasonType: [],
        reason: "",
        prevention: "",
        occurDept: "",
      },
      approvalRule: {
        occurDept: [{ required: true, message: "请选择发生部门/班组" }],
        reasonType: [{ required: true, message: "请选择原因类别", trigger: "blur" }],
        reason: [{ required: true, message: "请输入原因分析" }],
        prevention: [{ required: true, message: "请输入防止对策" }],
      },
      preventionHis: [],
      contentHis: [],
      scrapDialogType: "历史内容",
      submitBtn: "保存并提交审核",
    };
  },
  methods: {
    getDict() {
      findRepairList({ data: { type: 7 } }).then((res) => {
        this.scrapAttrDict = res.data.map((item) => {
          return {
            label: item.ngName,
            value: item.ngName,
          };
        });
      });
      getReasonHisApi().then((res) => {
        this.reasonTypeDict = res.data.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      });
      getPreventionHisApi().then((res) => {
        this.preventionHis = res.data;
      });
      getContentHisApi().then((res) => {
        this.contentHis = res.data;
      });
      selectOrganizationDepartment().then((res) => {
        res.data.forEach((item) => {
          this.scrapOccurDeptOption.push({
            dictCodeValue: item.name,
            dictCode: item.name,
          });
        });
      });
    },
    init() {
      this.getDict();
      this.submitBtn = "保存并提交审核";
      if (this.isEdit) {
        this.dialogTitle = "修改报废单";
        this.scrapRowParams = _.cloneDeep(this.currentScrapRow);
        this.ruleFrom.happenTime = this.scrapRowParams.occurTime;
        this.ruleFrom.scrapAttr = this.scrapRowParams.scrapAttr;
        this.ruleFrom.scrapContent = this.scrapRowParams.scrapContent;
        this.imageUrl = this.$getFtpPath(this.scrapRowParams.ngPicture);
        this.childTable.tableData = this.scrapRowParams.scrapList || [];
        this.sizeTable.tableData = this.scrapRowParams.dimensionList || [];
        // 2025/6/5删除部分节点，做出改动
        // if (this.approvalNode) {
        this.approvalRuleFrom.occurDept = this.scrapRowParams.occurDept;
        this.approvalRuleFrom.reasonType = this.scrapRowParams.reasonType
          ? this.scrapRowParams.reasonType.split(";")
          : this.scrapRowParams.reasonType;
        this.approvalRuleFrom.reason = this.scrapRowParams.reason;
        this.submitBtn = "提交审核";
        // }
        // if (this.approvalNode === "预防对策") {
        this.approvalRuleFrom.prevention = this.scrapRowParams.prevention;
        // }
      } else {
        this.dialogTitle = "创建报废单";
        this.childTable.tableData = _.cloneDeep(this.scrapList);
        let scrapContent = "";
        this.scrapList.map((item) => {
          if (item.rejectDescription) {
            scrapContent += `${item.batchNumber}:${item.rejectDescription}；\n`;
          }
        });
        this.ruleFrom.scrapContent = scrapContent;
      }
    },
    openScrapHistory(type) {
      this.showScrapContentListDialog = true;
      this.scrapDialogType = type;
    },
    // 保存历史内容
    saveHistory() {
      if (!this.ruleFrom.scrapContent) {
        this.$showWarn("请输入报废内容");
        return;
      }
      savePreventionApi({
        scrapContent: this.ruleFrom.scrapContent,
      }).then((res) => {
        this.$responsePrecedenceMsg(res).then(() => {
          getContentHisApi().then((res) => {
            this.contentHis = res.data;
          });
        });
      });
    },
    // 保存历史对策
    savePrevention() {
      savePreventionApi({
        prevention: this.approvalRuleFrom.prevention,
      }).then((res) => {
        this.$responsePrecedenceMsg(res).then(() => {
          getPreventionHisApi().then((res) => {
            this.preventionHis = res.data;
          });
        });
      });
    },
    // 选中历史内容或历史对策的回调
    handleSelect(row) {
      console.log(row);
      let content = "";
      row.forEach((item) => {
        content += `${item.scrapContent};`;
      });
      if (this.scrapDialogType === "历史内容") {
        this.ruleFrom.scrapContent
          ? (this.ruleFrom.scrapContent += `;${content}`)
          : (this.ruleFrom.scrapContent = content);
      } else {
        this.approvalRuleFrom.prevention
          ? (this.approvalRuleFrom.prevention += `;${content}`)
          : (this.approvalRuleFrom.prevention = content);
      }
    },
    changeAddImg(file, fileList) {
      const formData = new FormData();
      formData.append("file", file.raw);
      // console.log(fileList);
      getUploadNgPic(formData).then((res) => {
        this.originImgUrl = res.data;
        this.imageUrl = this.$getFtpPath(res.data);
        this.ruleFrom.scrapImg = this.imageUrl;
        console.log(this.imageUrl);
      });

      // 测试环境上传文件无法调通  测试环境调试图片效果用
      // let reader = new FileReader();
      // let vm = this;
      // reader.onload = (e) => {
      //   vm.imageUrl = e.target.result;
      //   this.ruleFrom.scrapImg = e.target.result;
      // };
      // reader.readAsDataURL(file.raw);
    },
    sizeNavClick(val) {
      switch (val) {
        case "选择不合格尺寸":
          if (!this.scrapRows.length) {
            this.$showWarn("请先勾选报废批次");
            return;
          }
          this.showSizeListDialog = true;
          break;
        case "移除":
          if (!this.sizeRows.length) {
            this.$showWarn("请勾选要移除的不良数据");
            return;
          }
          //删除勾选的不良尺寸
          this.sizeRows.forEach((sizeItem) => {
            this.sizeTable.tableData.forEach((item) => {
              if (sizeItem.batchNumber === item.batchNumber) {
                this.sizeTable.tableData.splice(this.sizeTable.tableData.indexOf(item), 1);
              }
            });
          });
          break;
        default:
          break;
      }
    },
    childNavClick(val) {
      switch (val) {
        case "移除":
          if (!this.scrapRows.length) {
            this.$showWarn("请勾选要移除的报废批次");
            return;
          }
          //删除勾选的报废批次所以删除对应添加的尺寸
          this.scrapRows.forEach((item) => {
            this.sizeTable.tableData.forEach((sizeItem, index) => {
              if (item.batchNumber === sizeItem.batchNumber) {
                this.sizeTable.tableData.splice(index, 1);
              }
            });
          });

          this.scrapRows.forEach((scrapItem) => {
            this.childTable.tableData.forEach((item) => {
              if (scrapItem.batchNumber === item.batchNumber) {
                this.childTable.tableData.splice(this.childTable.tableData.indexOf(item), 1);
              }
            });
          });

          break;
        default:
          break;
      }
    },
    // 勾选批次
    selectSizeRows(val) {
      this.sizeRows = val;
    },
    saveScrap() {
      let param = {
        occurTime: !this.ruleFrom.happenTime ? null : formatTimesTamp(this.ruleFrom.happenTime) || null,
        scrapAttr: this.ruleFrom.scrapAttr,
        scrapContent: this.ruleFrom.scrapContent,
        scrapList: this.childTable.tableData,
        dimensionList: this.sizeTable.tableData,
        ngPicture: this.originImgUrl,
      };
      param = Object.assign(param, this.approvalRuleFrom);
      param.reasonType = this.approvalRuleFrom.reasonType.join(";");
      getSaveScrapBill(param).then((res) => {
        // 处理成功提示信息
        this.$responseMsg(res).then(() => {
          this.$emit("addScrapHandle");
          this.$emit("update:showAddScrapDialog", false);
        });
      });
    },
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        this.$refs[formUser].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject(new Error("错误"));
          }
        });
      });
    },
    submitScrap(val1, val2) {
      Promise.all([this.submitForm(val1), this.submitForm(val2)])
        .then(() => {
          // if (this.approvalNode === "原因分析") {
          //   this.scrapRowParams = Object.assign(this.scrapRowParams, this.approvalRuleFrom);
          //   this.scrapRowParams.reasonType = this.scrapRowParams.reasonType.join(";");
          //   auditFillReasonApi(this.scrapRowParams).then((res) => {
          //     // 处理成功提示信息
          //     this.$responsePrecedenceMsg(res).then(() => {
          //       this.$emit("addScrapHandle");
          //       this.$emit("update:showAddScrapDialog", false);
          //     });
          //   });
          // } else if (this.approvalNode === "预防对策") {
          //   this.scrapRowParams = Object.assign(this.scrapRowParams, this.approvalRuleFrom);
          //   this.scrapRowParams.reasonType = this.scrapRowParams.reasonType.join(";");
          //   auditFillPreventionApi(this.scrapRowParams).then((res) => {
          //     // 处理成功提示信息
          //     this.$responsePrecedenceMsg(res).then(() => {
          //       this.$emit("addScrapHandle");
          //       this.$emit("update:showAddScrapDialog", false);
          //     });
          //   });
          // } else
          if (this.isEdit) {
            this.scrapRowParams.occurTime = !this.ruleFrom.happenTime
              ? null
              : formatTimesTamp(this.ruleFrom.happenTime) || null;
            this.scrapRowParams.scrapAttr = this.ruleFrom.scrapAttr;
            this.scrapRowParams.scrapContent = this.ruleFrom.scrapContent;
            this.scrapRowParams = Object.assign(this.scrapRowParams, this.approvalRuleFrom);
            this.scrapRowParams.reasonType = this.scrapRowParams.reasonType.join(";");
            this.originImgUrl && (this.scrapRowParams.ngPicture = this.originImgUrl);
            getStartScrapBill(this.scrapRowParams).then((res) => {
              // 处理成功提示信息
              this.$responsePrecedenceMsg(res).then(() => {
                this.$emit("addScrapHandle");
                this.$emit("update:showAddScrapDialog", false);
              });
            });
          } else {
            let param = {
              occurTime: !this.ruleFrom.happenTime ? null : formatTimesTamp(this.ruleFrom.happenTime) || null,
              scrapAttr: this.ruleFrom.scrapAttr,
              scrapContent: this.ruleFrom.scrapContent,
              scrapList: this.childTable.tableData,
              dimensionList: this.sizeTable.tableData,
              ngPicture: this.originImgUrl,
            };
            param = Object.assign(param, this.approvalRuleFrom);
            param.reasonType = this.approvalRuleFrom.reasonType.join(";");
            getStartScrapBill(param).then((res) => {
              // 处理成功提示信息
              this.$responseMsg(res).then(() => {
                this.$emit("addScrapHandle");
                this.$emit("update:showAddScrapDialog", false);
              });
            });
          }
        })
        .catch(() => {
          console.log("error submit!!");
          return false;
        });
    },
    closeAddScrap() {
      this.$emit("update:showAddScrapDialog", false);
    },
    selectScrapRows(val) {
      this.scrapRows = _.cloneDeep(val);
    },
    //点击子单行
    handleRowClick(val) {
      this.currentOperateChild = val;
      this.currentChildIndex = this.childTable.tableData.indexOf(val);
    },
    //选择尺寸回调
    selectSizesHandle(val) {
      val.forEach((item) => {
        this.sizeTable.tableData.push(item);
      });
    },
    qrCodeEnter() {
      getScanOneBatchScrap({
        batchNumber: this.qrCode,
      }).then((res) => {
        if (res.data) {
          let isAdded = false;
          this.childTable.tableData.forEach((item) => {
            if (item.batchNumber === res.data.batchNumber) {
              isAdded = true;
              return;
            }
          });
          if (isAdded) {
            this.$showWarn("当前批次已添加");
            return;
          }
          this.childTable.tableData.push(res.data);
        } else {
          this.$showWarn(res.status.message);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.custom-cell {
  padding: 0px 10px 0px 0px;
  width: 100%;
}
.ex-image {
  width: 200px;
}
</style>
