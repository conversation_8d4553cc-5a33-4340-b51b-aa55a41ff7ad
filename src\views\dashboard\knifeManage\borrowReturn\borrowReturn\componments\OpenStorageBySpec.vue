<template>
  <el-dialog
    
    title="打开托盘-规格"
    width="70vw"
    :visible="visible"
    @close="closeHanlder"
  >
    <div class="knife-selection-container">
      <!-- 刀具结构树 start -->
      <div class="constructor-tree">
        <ResizeButton
          v-model="resizeBtn.current"
          :max="resizeBtn.max"
          :min="resizeBtn.min"
          :isModifyParentWidth="true"
        />
        <div class="search-container">
          <div class="item-search">
            <el-input
              v-model="searchVal"
              @keyup.native.enter="typeNameFilter"
              placeholder="请输入类型名称查询"
              clearable
            />
            <el-button
              class="noShadow blue-btn"
              icon="el-icon-search"
              @click="typeNameFilter"
              >分类查询</el-button
            >
          </div>
          <hr />
          <div class="item-search mt4">
            <el-input
              v-model="searchSpecName"
              placeholder="请输入规格名称查询"
              @keyup.native.enter="specNameFilter"
              clearable
            />
            <el-button
              class="noShadow blue-btn"
              icon="el-icon-search"
              @click="specNameFilter"
              >规格查询</el-button
            >
          </div>
        </div>
        <span class="tree-title">
          <span>刀具结构树:</span>
        </span>
        <el-scrollbar>
          <el-tree
            v-if="visible && toggleTree"
            ref="tree"
            :data="menuList"
            node-key="unid"
            :props="defaultProps"
            :default-expand-all="defaultExpandAll"
            :default-expanded-keys="defaultExpKey"
            :filter-node-method="filterNode"
            :highlight-current="true"
            :currentNodeKey="this.curSpecRow.unid"
            show-checkbox
            @node-click="menuClick"
            @node-expand="menuClick"
          >
            <div
              slot-scope="{ node, data }"
              :class="['custom-tree-node', 'tr', 'row-between']"
              style="width: 100%"
            >
              <!-- label: 代表分类名，specName: 规格名称 -->
              <span>{{ node.label || data.specName }}</span>
            </div>
          </el-tree>
        </el-scrollbar>
      </div>
      <el-tooltip content="查询结果默认勾选">
        <el-button class="noShadow blue-btn search-btn" @click="searchSpec"
          >查询</el-button
        >
      </el-tooltip>
      <div class="basic-content">
        <el-form
          ref="searchFormEle"
          class="reset-form-item clearfix"
          :model="searchData"
          inline
          label-width="110px"
          @submit.native.prevent
        >
          <el-form-item
            class="el-col el-col-8"
            label="规格名称"
            prop="specName"
          >
            <el-input
              v-model="searchData.specName"
              placeholder="请输入规格名称"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            :label="$FM() ? '库位' : '货架'"
            prop="storageLocation"
          >
            <el-input
              v-model="searchData.storageLocation"
              :placeholder="`请输入${$FM() ? '库位' : '货架'}`"
            />
          </el-form-item>

          <el-form-item :class="`align-r el-col el-col-8`">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchHandler"
              >查询</el-button
            >
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetHandler"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <nav-bar :nav-bar-list="navBarConfig" @handleClick="navClickHandler" />
        <vxe-table
          border
          height="500px"
          ref="xTable1"
          :data="tableData"
          :checkbox-config="{ trigger: 'row' }"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="40"></vxe-column>
          <vxe-column
            field="typeName"
            title="刀具类型"
            show-overflow
          ></vxe-column>
          <vxe-column
            field="specName"
            title="规格名称"
            show-overflow
          ></vxe-column>
          <vxe-column
            field="storageLocation"
            :title="$FM() ? '库位' : '货架'"
            show-overflow
          ></vxe-column>
        </vxe-table>
      </div>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitHandler"
        >打开托盘</el-button
      >
      <el-button class="noShadow red-btn" @click="cancelHandler"
        >取消</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
  import ResizeButton from "@/components/ResizeButton/ResizeButton";
  import NavBar from "@/components/navBar/navBar";
  import { findName, setEmptyTm, getLastCatalog } from "@/utils/until";
  import {
    getCatalogTree,
    getMasterProperties,
    findAllByCatalogTreeBySpecName,
  } from "@/api/knifeManage/basicData/specMaintain";
  import {
    selectCutterStorageSpaceByCatalog,
    openPalletBySpecIds,
  } from "@/api/knifeManage/basicData/cutterCart";
  let tempCatalogId = [],
    tempSpecId = [];
  export default {
    name: "OpenStorageBySpec",
    components: {
      ResizeButton,
      NavBar,
    },
    props: {
      visible: {
        type: Boolean,
        require: true,
        default: false,
      },
    },
    data() {
      return {
        resizeBtn: {
          current: { x: 270, y: 0 },
          max: { x: 400, y: 0 },
          min: { x: 250, y: 0 },
        },
        // 搜索字段
        searchVal: "",
        searchSpecName: "",
        // 结构树数据
        menuList: [],
        defaultProps: {
          children: "catalogTMs",
          label: "name",
        },
        // 当前选中的类型
        curCataLogRow: {},
        // 当前选中的规格
        curSpecRow: {},
        oldMenuList: [],
        defaultExpandAll: false,
        toggleTree: true,
        lastCatalogId: [],
        lastSpecId: [],
        searchData: {
          specName: "",
          storageLocation: "",
        },
        tableData: [],
        tableDataSelectedData: [],
        navBarConfig: {
          title: "规格列表",
          list: [
            {
              Tname: "批量取消",
              key: "batchCancel",
            },
          ],
        },
      };
    },
    watch: {
      visible: {
        immediate: true,
        handler(v) {
          if (v) {
            this.setTableData();
            this.getCatalogTree();
          }
        },
      },
    },
    computed: {
      defaultExpKey() {
        const [{ unid = "" } = {}] = this.curCataLogRow.catalogTMs || [{}];
        return [unid];
      },
    },
    methods: {
      navClickHandler(k) {
        this[k] && this[k]();
      },
      getCurrentKey() {
        const curCheckedKeys = this.$refs.tree.getCheckedKeys();
        const lastCatalogId = curCheckedKeys.filter((k) =>
          this.lastCatalogId.includes(k)
        );
        const lastSpecId = curCheckedKeys.filter((k) =>
          this.lastSpecId.includes(k)
        );
        return { lastCatalogId, lastSpecId };
      },
      closeHanlder() {
        this.cancelHandler();
        // this.toggleVisible(false);
      },
      toggleVisible(v) {
        this.$emit("update:visible", v);
      },
      typeNameFilter() {
        this.toggleTree = false;
        this.defaultExpandAll = false;
        this.menuList = _.cloneDeep(this.oldMenuList);
        this.$nextTick(() => {
          this.toggleTree = true;
          this.$nextTick(() => {
            this.$refs.tree.filter(this.searchVal);
          });
        });
      },
      specNameFilter() {
        if (this.searchSpecName.trim() === "") {
          this.menuList = _.cloneDeep(this.oldMenuList);
          this.toggleTree = false;
          this.defaultExpandAll = false;
          this.curCataLogRow = {};
          this.curSpecRow = {};
          this.$nextTick(() => {
            this.toggleTree = true;
          });
          return;
        }
        this.toggleTree = false;
        this.findAllByCatalogTreeBySpecName();
      },
      filterNode(value, data, node) {
        if (!value) return true;
        const name = data.name || data.specName || "";
        return findName(value, node.parent) || name.indexOf(value) !== -1;
      },
      // 菜单的点击事件（请求规格）
      menuClick(row) {
        // 最后一级类别存为临时项
        // 非最后一级分类、规格列都无需请求
        this.curSpecRow = {};
        if (row.type !== "2" && row.catalogTMLast) {
          this.curCataLogRow = row;
          // console.log(row, 'row')
          // if (this.defaultExpandAll) {
          //   // this.curCataLogRow.catalogTMs = this.curCataLogRow.masterProperties;
          //   return
          // }
          this.getMasterProperties();
        }

        // 如果选中的规格
        if (row.type === "2") {
          this.curSpecRow = _.cloneDeep(row);
        }
      },
      async findAllByCatalogTreeBySpecName() {
        try {
          const { data = [] } = await findAllByCatalogTreeBySpecName(
            this.searchSpecName
        );
          tempCatalogId = [];
          tempSpecId = [];
          deepChangeKey(data);
          this.lastSpecId = [...tempSpecId, ...this.lastSpecId];
          this.defaultExpandAll = true;
          this.menuList = data;
          this.$nextTick(() => {
            this.toggleTree = true;
            const res = deepFindSpec(this.curSpecRow.unid, this.menuList);
            if (res) {
              this.curSpecRow = res;
            } else {
              this.curSpecRow = {};
            }
          });
          } catch (e) {
          console.log(e, "data[i].catalogTMLast = false");
        }
      },
      // 查询刀具类型树
      async getCatalogTree() {
        try {
          const { status: { success } = {}, data } = await getCatalogTree({});
          if (success) {
            const lastCatalog = [];
            getLastCatalog(data, lastCatalog);
            this.lastCatalogId = lastCatalog.map(({ unid }) => unid);
            setEmptyTm(data);
            this.menuList = data;
            this.oldMenuList = _.cloneDeep(data);
          }
        } catch (e) {}
      },
      // 查询刀具规格
      async getMasterProperties() {
        try {
          const { status: { success } = {}, data } = await getMasterProperties({
            catalogId: this.curCataLogRow.unid,
          });

          if (success) {
            if (data.length) {
              console.log(data, "data------------");
              this.curCataLogRow.catalogTMs = data;
              const ids = data.map(({ unid }) => unid);
              this.lastSpecId = [...ids, ...this.lastSpecId];
              this.$set(this.curCataLogRow, "catalogTMs", data);
              this.curSpecRow =
                data.find((it) => it.unid === this.curSpecRow.unid) || {};
              this.curSpecRow.unid &&
                this.$nextTick(() => {
                  this.$refs.tree.setCurrentKey(this.curSpecRow.unid);
                });
            } else {
              this.curCataLogRow.catalogTMs = [
                { isEmpty: true, specName: "暂无数据" },
              ];
              this.curSpecRow = {};
            }
            this.oldMenuList = _.cloneDeep(this.menuList);
            // this.curCataLogRow.catalogTMLast = false
          }
        } catch (e) {
          console.log(e, "e");
        }
      },
      cancelHandler() {
        this.$refs.searchFormEle.resetFields();
        this.tableData = [];
        this.tableDataSelectedData = [];
        this.searchVal = "";
        this.searchSpecName = "";
        this.toggleVisible(false);
      },
      submitHandler() {
        if (!this.tableDataSelectedData.length) {
          this.$showWarn("请先选择规格库位~");
          return;
        }
        this.$handleCofirm(
          `是否打开当前${this.tableDataSelectedData.length}条规格的托盘`
        ).then(() => {
          // this.$emit("save", _.cloneDeep(this.tableDataSelectedData));
          // this.$emit("update:visible", false);
          // this.$showSuccess("保存成功");
          this.openPellet()
        });
      },
      async searchSpec() {
        const { lastCatalogId, lastSpecId } = this.getCurrentKey();
        const { data } = await selectCutterStorageSpaceByCatalog({
          data: { unids: lastCatalogId, specIds: lastSpecId },
        });
        // const canPushList = [];
        // data.forEach((dIt) => {
        //   const index = this.tableData.findIndex(
        //     (tIt) => tIt.specId === dIt.specId
        //   );
        //   if (index === -1) {
        //     canPushList.push(dIt);
        //   }
        // });

        // this.setTableData([...canPushList, ...this.tableData]);
        // this.setTableData([...canPushList, ...this.tableData]);
        const oldData = _.cloneDeep(this.tableData)
        const canPush = []
        data.forEach(nData => {
          const index = oldData.findIndex(oData => oData.specId === nData.specId)
          if (index === -1) {
            canPush.push(nData)
          }
        })
        this.setTableData([...canPush, ...oldData]);
        this.resetHandler();
      },

      searchHandler() {
        let { specName, storageLocation } = this.searchData;

        specName = specName.trim();
        storageLocation = storageLocation.trim();

        if (!storageLocation && !specName) {
          this.resetTableData();
          return;
        }

        const tableData = this.getTableData();

        this.tableData = tableData.filter((it) => {
          let resFlag = true;
          if (storageLocation && it.storageLocation) {
            resFlag = it.storageLocation.includes(storageLocation);
          }

          if (specName && it.specName) {
            resFlag = it.specName.includes(specName);
          }

          return resFlag;
        });
      },
      setTableData(arr = []) {
        localStorage.setItem("storageSpecList", JSON.stringify(arr));
      },
      getTableData() {
        const specList = localStorage.getItem("storageSpecList");
        return specList ? JSON.parse(specList) : [];
      },
      resetTableData() {
        this.tableData = this.getTableData();
        this.tableDataSelectedData = this.tableData;
        this.$nextTick(() => {
          this.$refs.xTable1.setCheckboxRow(this.tableData, true);
        });
      },
      resetHandler() {
        this.$refs.searchFormEle.resetFields();
        this.$nextTick(() => {
          this.resetTableData();
        });
      },
      selectAllEvent(config) {
        this.tableDataSelectedData = config.records;
      },
      selectChangeEvent(config) {
        this.tableDataSelectedData = config.records;
      },
      batchCancel() {
        if (!this.tableDataSelectedData.length) {
          this.$showWarn("请勾选需要取消的规格~");
          return;
        }
        this.$handleCofirm(
          `是否取消已勾选的规格(${this.tableDataSelectedData.length}条)?`
        ).then(() => {
          const tableData = this.getTableData();
          this.tableDataSelectedData.forEach(({ specId }) => {
            const index = tableData.findIndex((it) => it.specId === specId);
            tableData.splice(index, 1);
          });

          this.tableDataSelectedData = [];
          this.setTableData(tableData);
          this.resetHandler();
          this.$showSuccess("取消成功~");
        });
      },
      async openPellet() {
        try {
          const specIds = this.tableDataSelectedData.map(({ specId }) => specId);
          // specIds
          const { data, status } = await openPalletBySpecIds(specIds);
          
          if (status.code === 400) {
            data && this.$parent.toggleErrorSpec(true, data);

            let aT = setTimeout(() => {
              this.$showWarn(status.message);
              clearTimeout(aT);
            }, 600);
          }
          if (status.code === 200) {
            data && this.$parent.toggleErrorSpec(true, data, 1);

            let aT = setTimeout(() => {
              this.$showWarn(status.message);
              clearTimeout(aT);
              this.cancelHandler()
            }, 600);
          }
        } catch (e) {
          console.log(e, "e");
        }
      },
    },
  };

  function deepChangeKey(data) {
    for (let i = 0; i < data.length; i++) {
      const { type, catalogTMs, masterProperties } = data[i];
      if (
        (type === "1" || type === null) &&
        catalogTMs.length === 0 &&
        masterProperties &&
        masterProperties.length
      ) {
        data[i].catalogTMLast = true;
        data[i].catalogTMs = data[i].masterProperties;
      } else if ((type === "1" || type === null) && catalogTMs.length) {
        deepChangeKey(catalogTMs);
        data[i].catalogTMLast = false;
      }
    }
  }
  function deepFindSpec(valUnid, arr) {
    for (let i = 0; i < arr.length; i++) {
      const { type, catalogTMs, masterProperties, unid } = arr[i];
      if (!catalogTMs) {
        if (unid === valUnid) {
          return arr[i];
        }
      } else {
        const res = deepFindSpec(valUnid, catalogTMs);
        if (res) return res;
      }
    }
  }
</script>
<style lang="scss">
  .knife-selection-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .constructor-tree {
      height: 540px;
      min-width: 16%;
      max-width: 18%;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      border: 1px solid #ebeef5;
      background-color: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      user-select: none;
      .tree-title {
        display: flex;
        justify-content: space-between;
        margin-top: 6px;

        .el-icon-refresh {
          cursor: pointer;
        }
      }

      display: flex;
      flex-direction: column;
      .el-scrollbar {
        flex: 1;
        .el-scrollbar__wrap {
          overflow-x: hidden;
          .el-tree {
            padding-right: 5px;
          }
        }
      }

      .search-container
        .el-input__suffix
        .el-input__suffix-inner
        .el-input__icon {
        line-height: 26px !important;
      }

      .search-container {
        .item-search {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          .el-input {
            width: 66%;
          }

          .el-button {
            padding: 6px;
          }

          &.mt4 {
            margin-top: 4px;
          }
        }
      }
    }

    .basic-content {
      flex: 1;
      // height: 600px;
      overflow-x: auto;
      background-color: #fff;
    }

    .search-btn {
      margin: 0 10px;
      height: 28px;
    }
  }

  .recycleBin-drawer {
    .el-drawer__header {
      margin-bottom: 16px;
    }

    .recycleBin-drawer-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }
</style>
