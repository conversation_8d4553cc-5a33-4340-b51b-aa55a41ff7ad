<template>
	<el-dialog
		:visible.sync="dialogData.visible"
		:title="dialogData.title"
		width="800px"
		:append-to-body="true"
		@close="cancel">
		<el-form ref="formBase" :model="formData" :rules="formRules">
			<form-item-control
				:list="baseFormList"
				:form-data="formData"
				@subButtonClick="subButtonClick"
				@change="changeHanlder"
				com-class="el-col el-col-12" />
		</el-form>
		<NavBar :nav-bar-list="multitProcessOutsourceMsgInfo"></NavBar>
		<el-row class="batchStyle" v-if="dialogData.title == '新增委外单'">
			<el-col :span="2" class="lable">批次号</el-col>
			<el-col :span="10" class="search">
				<ScanCode
					ref="scanPsw"
					v-model="batchNumber"
					placeholder="扫描录入（批次号）"
					@handleClear="handlebanchClear"
					@enter="searchClick" />
			</el-col>
			<el-col :span="2" class="search">
				<el-button class="noShadow blue-btn" @click="remove" type="primary">移除</el-button>
			</el-col>
			<el-col :span="2" class="search">
				<el-button class="noShadow blue-btn" @click="selectworkOrder" type="primary">工单</el-button>
			</el-col>
		</el-row>
		<vTable :table="multitProcessOutsourceMsg" @checkData="multitProcessCheckData" checked-key="id" />
		<NavBar :nav-bar-list="processOutsourceMsgInfo"></NavBar>
		<vTable
			ref="processOutsourceTable"
			:table="processOutsourceMsg"
			:isCurChecDataRow="false"
			@getRowData="getRowData"
			checked-key="id" />
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="handleSaveProcess('keepApproval')">
				保存并发起审批
			</el-button>
			<el-button class="noShadow blue-btn" type="primary" @click="handleSaveProcess()">确认</el-button>
			<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
		</div>
		<businessSupplier
			:dialogData="businessSupplierDialog"
			@businessSupplierInfo="businessSupplierInfo"></businessSupplier>
		<workOrderInfoDialog
			:dialogData="workOrderInfo"
			@workOrderInfoDialog="workOrderInfoDialog"></workOrderInfoDialog>
		<AuditTemplate :dialogData="auditDg" auditTemplateId="90" @auditTemplate="handleAuditTemplate"></AuditTemplate>
	</el-dialog>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import FormItemControl from "@/components/FormItemControl/indexV1.vue";
import businessSupplier from "@/components/businessSupplier";
import workOrderInfoDialog from "./workOrderInfoDialog";
import AuditTemplate from "@/components/auditTemplate";
import _ from "lodash";

import {
	startApproval,
	addFPpOutsourcingOrder,
	findFPpOrderBatch,
	updateFPpOutsourcingOrder,
} from "@/api/courseOfWorking/outsourceMsg";

const outsourceDtl = {
	title: "多工序委外详情",
	list: [],
};
const baseInfo = {
	title: "基本信息",
	list: [],
};
const multitProcessOutsourceMsgInfo = {
	title: "多工序委外管理",
	list: [],
};

function isContinuous(sequence) {
    if (!Array.isArray(sequence) || sequence.length === 0) {
        return {
            valid: false,
            message: "请选择工序"
        };
    }
    if (sequence.length === 1) {
        return {
            valid: true,
            message: ""
        };
    }
    
    // 按 index 排序，确保按实际工序顺序排列
    const sortedSequence = _.sortBy(sequence, "index");
    
    // 找出最小和最大的index
    const minIndex = sortedSequence[0].index;
    const maxIndex = sortedSequence[sortedSequence.length - 1].index;
    
    // 检查是否有遗漏的工序
    const selectedIndexSet = new Set(sortedSequence.map(item => item.index));
    for(let i = minIndex; i <= maxIndex; i++) {
        if(!selectedIndexSet.has(i)) {
            // 找出缺失工序前后的工序名称
            const prevStep = sortedSequence.find(item => item.index === i - 1);
            const nextStep = sortedSequence.find(item => item.index === i + 1);
            return {
                valid: false,
                message: `请选择连续工序`
            };
        }
    }
    
    return {
        valid: true,
        message: ""
    };
}

const processOutsourceMsgInfo = {
	title: "工序委外管理",
	list: [],
};
export default {
	name: "ProcessOutsourceDtl",
	components: {
		vTable,
		NavBar,
		ScanCode,
		FormItemControl,
		businessSupplier,
		workOrderInfoDialog,
		AuditTemplate,
	},
	inject: ["PRODUCTION_BATCH_STATUS_SUB", "REASONS_OUTSOUR"],
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},

	data() {
		return {
			batchNumber: "",
			outsourceDtl,
			baseInfo,
			formData: { reason: "", supplierName: "", reasonDescription: null },
			baseFormList: [
				{
					prop: "reason",
					label: "委外原因",
					type: "select",
					placeholder: "请输入委外原因",
					options: [],
				},

				{
					prop: "supplierName",
					label: "供应商",
					placeholder: "请输入选择",
					disabled: true,
					buttonDisabled: false,
					type: "input",
					sub: {
						type: "button",
						lable: "选择",
					},
				},
				// {
				// 	prop: "isStartApproval",
				// 	label: "是否自动发起审批",
				// 	type: "select",
				// 	placeholder: "请选择",
				// 	labelWidth: "150px",
				// 	options: [
				// 		{
				// 			value: "0",
				// 			label: "是",
				// 		},
				// 		{
				// 			value: "1",
				// 			label: "否",
				// 		},
				// 	],
				// },
				{
					prop: "reasonDescription",
					label: "具体原因",
					type: "input",
					hidden: true,
					placeholder: "请输入具体原因",
				},
			],
			formRules: {
				supplierName: [{ required: true, message: "请输入供应商", trigger: "blur" }],
				reason: [{ required: true, message: "委外原因", trigger: "blur" }],
				// isStartApproval: [{ required: true, message: "请选择", trigger: "change" }],
			},

			multitProcessOutsourceMsgInfo,
			multitProcessOutsourceMsg: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "制番号", prop: "makeNo" },
					{ label: "内部图号", prop: "innerProductNo" },
					{
						label: "数量",
						prop: "quantityInt",
					},
					{
						label: "当前工序名称",
						prop: "nowStepName",
					},
					{
						label: "状态小类",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB(), row.statusSubclass);
						},
						width: "80",
					},
				],
			},
			processOutsourceMsgInfo,
			processOutsourceMsg: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
				isSelectAll: true,
				sequence: false,
				check: true,
				isFit: false,
				tabTitle: [
					{ label: "序号", prop: "seqNo" },
					{
						label: "工序编码",
						prop: "stepCode",
					},
					{ label: "工序名称", prop: "stepName" },
					{
						label: "是否返修",
						prop: "proId",
						render: (row) => {
							return row.proId ? "是" : "否";
						},
					},
				],
			},
			businessSupplierDialog: {
				visible: false,
				rowList: [],
			},
			auditDg: {
				visible: false,
				title: "发起审批",
			},
			multitProcessCheckRow: "",
			processOutsourceRowList: [],
			//弹框配置
			workOrderInfo: {
				visible: false,
				wholeProcess: [],
			},
			approvalData: {},
		};
	},
	watch: {
		"dialogData.visible"(val) {
			this.baseFormList.map((item) => {
				if (item.prop == "reason") {
					item.options = this.REASONS_OUTSOUR().map((dictItem) => {
						return {
							label: dictItem.dictCodeValue,
							value: dictItem.dictCodeValue,
						};
					});
				}
			});
			if (val) {
				this.processOutsourceMsg.isSelectAll = true;
				this.handleEditData();
			}
		},
	},
	methods: {
		businessSupplierInfo(info) {
			this.$set(this.formData, "supplierCode", info.supplierCode);
			this.$set(this.formData, "supplierName", info.supplierName);
		},
		handleEditData() {
			if (this.dialogData.rowData.id) {
				this.processOutsourceMsg.isSelectAll = false;
				this.formData.reason = this.dialogData.rowData.reason;
				this.formData.supplierName = this.dialogData.rowData.supplierName;
				this.formData.supplierCode = this.dialogData.rowData.supplierCode;
				this.formData.id = this.dialogData.rowData.id;
				this.batchNumber = this.dialogData.rowData.batchNumber;
				this.multitProcessOutsourceMsg.tableData = [this.dialogData.rowData];
				this.handleFindFPpOrderBatchEdit();
				// if (this.dialogData.rowData.stepPos.length > 0) {
				// 	this.handleGdata(this.dialogData.rowData.stepPos);
				// }
			}
		},
		changeHanlder(val) {
			if (val.prop !== "reasonDescription") {
				const index = this.baseFormList.findIndex((item) => item.prop == "reasonDescription");
				if (index !== -1) {
					this.baseFormList[index].hidden = !(val.prop == "reason" && val.value == "其它");
					this.formData.reasonDescription = "";
				}
			}
		},
		handlebanchClear() {
			this.batchNumber = "";
		},
		subButtonClick(val) {
			if (val.prop == "supplierName") {
				this.businessSupplierDialog.visible = true;
			}
		},
		async searchClick(val) {
			if (!this.batchNumber) {
				return this.$message.warning("请输入正确批次号");
			}
			this.handleFindFPpOrderBatch();
		},
		async handleFindFPpOrderBatchEdit() {
			const { data } = await findFPpOrderBatch({
				batchNumber: this.batchNumber,
				wholeProcess: this.processOutsourceMsg.tableData,
			});
			this.handleGdata(data.wholeProcess);
		},
		async handleFindFPpOrderBatch() {
			const { data } = await findFPpOrderBatch({
				batchNumber: this.batchNumber,
				wholeProcess: this.processOutsourceMsg.tableData,
			});
			this.multitProcessOutsourceMsg.tableData = _.uniqBy(
				[...this.multitProcessOutsourceMsg.tableData, data],
				"id"
			);

			this.handleGdata(data.wholeProcess);
		},
		selectworkOrder() {
			this.workOrderInfo.wholeProcess = this.processOutsourceMsg.tableData;
			this.workOrderInfo.visible = true;
		},
		//  出来委外批次工序数据
		handleGdata(val) {
			if (val.length > 0) {
				const list = val.map((item, index) => {
					return {
						...item,
						index: index + 1,
					};
				});

				this.$set(this.processOutsourceMsg, "tableData", list);
				if (this.dialogData.rowData.id) {
					this.setSelectRow(this.dialogData.rowData.stepPos);
				}
			}
		},
		setSelectRow(stepPos) {
			this.$nextTick(() => {
				if (this.$refs.processOutsourceTable && stepPos?.length) {
					// // 先清空所有选中状态
					// this.$refs.processOutsourceTable.clearSelection();
					// 根据 stepId 找到对应的行并选中
					const rows = this.processOutsourceMsg.tableData.filter((row) =>
						stepPos.some((item) => item.stepId === row.id)
					);
					this.processOutsourceRowList = rows;
					// 逐个选中行
					setTimeout(() => {
						rows.forEach((row) => {
							this.$refs.processOutsourceTable.selToggleRowSelection(row);
						});
					}, 0);
				}
			});
		},
		multitProcessCheckData(val) {
			this.multitProcessCheckRow = val;
			// if (val.wholeProcess && val.wholeProcess.length) {
			// 	this.handleGdata(val.wholeProcess);
			// }
		},
		workOrderInfoDialog(val) {
			if (this.multitProcessOutsourceMsg.tableData.length === 0) {
				this.multitProcessOutsourceMsg.tableData = [...val];
			} else {
				this.multitProcessOutsourceMsg.tableData = _.uniqBy(
					[...this.multitProcessOutsourceMsg.tableData, ...val],
					"id"
				);
			}
			this.handleGdata(val[0].wholeProcess);
		},
		getRowData(rowList) {
			this.processOutsourceRowList = _.uniqBy([...rowList],"id" )
		},
		remove() {
			const index = this.multitProcessOutsourceMsg.tableData.findIndex(
				(item) => item.id === this.multitProcessCheckRow.id
			);
			if (index !== -1) {
				this.multitProcessOutsourceMsg.tableData.splice(index, 1);
			}
			if (this.multitProcessOutsourceMsg.tableData.length === 0) {
				this.processOutsourceMsg.tableData = [];
			}
		},
		async handleSaveProcess(type) {
			if (this.multitProcessOutsourceMsg.tableData.length === 0) {
				return this.$message.warning("请先选择批次号");
			}

			const continuityCheck = isContinuous(this.processOutsourceRowList);
			if (!continuityCheck.valid) {
				return this.$message.warning(continuityCheck.message);
			}

			const stepPosList = this.processOutsourceRowList.map((item) => {
				return {
					...item,
					unid: item.id,
				};
			});
			const params = {
				...this.formData,
				batches: this.multitProcessOutsourceMsg.tableData,
				stepPos: stepPosList,
			};
			this.$refs["formBase"].validate(async (valid) => {
				if (valid) {
					try {
						let data = null;
						if (this.dialogData.title == "修改委外单") {
							data = await updateFPpOutsourcingOrder(params);
						} else {
							data = await addFPpOutsourcingOrder(params);
						}
						const {
							status: { message, code },
						} = data;
						if (code !== 200) {
							return this.$message.error(message);
						}
						if (type == "keepApproval") {
							this.auditDg.visible = true;
							if (this.dialogData.title == "修改委外单") {
								this.approvalData = [data.data];
							} else {
								this.approvalData = data.data;
							}
						} else {
							this.$message.success(message);
							this.$parent.searchClick();
						}
						this.cancel();
					} catch (error) {
						console.log(error);
					}
				} else {
					return false;
				}
			});
		},
		async handleAuditTemplate(val) {
			const idList = this.approvalData.map((item) => item.id);
			const formData = {
				idList,
				approvalTemplateId: val.unid,
			};
			const {
				status: { message, code },
			} = await startApproval(formData);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.$parent.searchClick();
			this.$message.success("发起审核成功");
			this.auditDg.visible = false;
			this.cancel();
		},
		cancel() {
			this.dialogData.rowData = {};
			this.dialogData.visible = false;
			this.formData = { reason: "", supplierName: "", reasonDescription: "" };
			this.$refs.formBase.resetFields();
			this.batchNumber = "";
			this.multitProcessOutsourceMsg.tableData = [];
			this.processOutsourceMsg.tableData = [];
			this.changeHanlder({ prop: "", value: "" });
		},
	},
};
</script>

<style lang="scss" scoped>
.batchStyle {
	vertical-align: middle;
	.lable {
		margin-top: 10px;
	}
	.search {
		margin-top: 7px;
		margin-left: 10px;
	}
}
.operation-btn {
	display: flex;
	justify-content: flex-start;

	.operation-btn-item {
		width: 80px;
		height: 60px;
		text-align: center;
		div:nth-child(1) {
			line-height: 30px;
			font-size: 25px;
		}
		div:nth-child(2) {
			line-height: 30px;
		}
		&:hover {
			background-color: #f5f5f5;
		}
	}
}
</style>
