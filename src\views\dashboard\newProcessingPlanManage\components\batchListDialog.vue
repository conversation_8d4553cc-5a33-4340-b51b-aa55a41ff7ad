<!--
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2025-07-15 15:21:03
-->
<template>
	<el-dialog
		title="批次列表"
		width="92%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showBatchListDialog"
		append-to-body>
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleForm">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-6" label="制番号" label-width="80px" prop="makeNo">
					<el-input
						v-model="ruleForm.makeNo"
						:disabled="mode != 'addExistedBatch'"
						clearable
						placeholder="请输入制番号"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="当前工序" label-width="80px" prop="nowStepCodeList">
					<el-select
            :disabled="workOrderCodeList.length > 1"
						v-model="ruleForm.nowStepCodeList"
						placeholder="请选择当前工序"
						clearable
						multiple
						filterable>
						<el-option
							v-for="item in stepDict"
							:key="item.value"
							:label="item.label"
							:value="item.value"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="质量状态" label-width="80px" prop="ngStatusList">
					<el-select
						v-model="ruleForm.ngStatusList"
						placeholder="请选择质量状态"
						clearable
						multiple
						filterable>
						<el-option
							v-for="item in ngStatusDict"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="入库状态" label-width="80px" prop="warehousStatusList">
					<el-select
						v-model="ruleForm.warehousStatusList"
						placeholder="请选择入库状态"
						multiple
						clearable
						filterable>
						<el-option
							v-for="item in warehoursStatusDict"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-6" label="工单号" label-width="80px" prop="workOrderCode">
					<el-input
						v-model="ruleForm.workOrderCode"
						clearable
						:disabled="mode != 'addExistedBatch'"
						placeholder="请输入工单号"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="投料状态" label-width="80px" prop="throwStatusList">
					<el-select
						v-model="ruleForm.throwStatusList"
						placeholder="请选择投料状态"
						clearable
						multiple
						filterable>
						<el-option
							v-for="item in throwStatusDict"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="操作状态" label-width="80px" prop="pauseStatusList">
					<el-select
						v-model="ruleForm.pauseStatusList"
						placeholder="请选择操作状态"
						clearable
						multiple
						filterable>
						<el-option
							v-for="item in pauseStatusDict"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="批次状态" label-width="80px" prop="batchStatusList">
					<el-select
						v-model="ruleForm.batchStatusList"
						placeholder="请选择批次状态"
						clearable
						multiple
						filterable>
						<el-option
							v-for="item in batchStatusDict"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-6" label="内部图号" label-width="80px" prop="innerProductNo">
					<el-input
						v-model="ruleForm.innerProductNo"
						:disabled="mode != 'addExistedBatch'"
						clearable
						placeholder="请输入内部图号"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="物料编码" label-width="80px" prop="partNo">
					<el-input
						v-model="ruleForm.partNo"
						:disabled="mode != 'addExistedBatch'"
						clearable
						placeholder="请输入物料编码"></el-input>
				</el-form-item>

				<el-form-item class="el-col el-col-6" label="创建日期" label-width="80px" prop="time">
					<el-date-picker
						v-model="ruleForm.time"
						clearable
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="timestamp"
						:default-time="['00:00:00', '23:59:59']" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="工艺路线版本" label-width="120px" prop="routeVersion">
					<el-input
            v-model="ruleForm.routeVersion"
            placeholder="请选择工艺路线版本"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openCraft"
            />
          </el-input>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-4 fr pr" label-width="-15px">
					<el-button
						native-type="submit"
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="submit('1')">
						查询
					</el-button>
					<el-button
						class="noShadow red-btn"
						size="small"
						icon="el-icon-refresh"
						@click="resetFrom('proPFrom')">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<div style="max-height: 550px; overflow: hidden; overflow-y: scroll">
			<NavBar v-if="mode == 'print'" :nav-bar-list="batchNavBarList" @handleClick="batchNavClick">
				<template #right>
					<ScanCode
						style="margin-left: 32px"
						v-model="qrCode"
						:lineHeight="25"
						:markTextTop="0"
						:first-focus="false"
						@enter="qrCodeEnter"
						placeholder="批次扫描框" />
				</template>
			</NavBar>
			<vTable
				:table="table"
				:needEcho="true"
				@checkData="checkRow"
				@getRowData="selectBatchRows"
				@changePages="changePages"
				@changeSizes="changeSize"
				checkedKey="id" />
		</div>
		<div slot="footer">
			<el-button v-if="mode == 'print'" class="noShadow blue-btn" type="primary" @click="printBatch">
				打印批次
			</el-button>
			<el-button v-if="mode == 'print'" class="noShadow blue-btn" type="primary" @click="printLetterBatch">
				打印批次（刻字）
			</el-button>
      <el-button v-if="mode == 'print'" class="noShadow blue-btn" type="primary" @click="siOrderNoticePrint">
				SI生产订单通知单打印
			</el-button>
			<el-button v-if="mode != 'print'" class="noShadow blue-btn" type="primary" @click="submitMark">
				确 定
			</el-button>
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
		<!-- 工艺路线弹窗 -->
		<template v-if="craftFlag">
			<CraftMark :flag.sync="craftFlag" :datas="craftData" @selectRow="selecrCraftRow" />
		</template>
	</el-dialog>
</template>
<script>
import { batchDialogList } from "../js/column.js";
import NavBar from "@/components/navBar/navBar";
import ScanCode from "@/components/ScanCode/ScanCode";
import vTable from "@/components/vTable2/vTable.vue";
import { searchDict } from "@/api/productOrderManagement/productOrderManagement.js";
import CraftMark from "./craftDialog2.vue";
import {
	productionBatchSearchByWorkOrderCode,
	getBatchDetailByScan,
	getProductRouteByWorkOrderCode,
	productionBatchSearch,
} from "@/api/workOrderManagement/workOrderManagement.js";
import { formatTimesTamp,formatYD } from "@/filters/index.js";
import _ from "lodash";
export default {
	name: "batchListDialog",
	components: {
		vTable,
		NavBar,
		ScanCode,
		CraftMark
	},
	props: {
		mode: {
			type: String,
			default: "",
		},
		showBatchListDialog: {
			type: Boolean,
			default: false,
		},
		workOrderDetail: {
			type: Object,
			default: () => {},
		},
		workOrderCodeList: {
			type: Array,
			default: () => [],
		},
		addedBatchList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			craftFlag:false,
			craftData: {},
			rowData: {},
			rowDatas: [],
			table: {
				tableData: [],
				tabTitle: [],
			},
			stepDict: [],
			batchStatusDict: [],
			runStatusDict: [],
			ngStatusDict: [],
			pauseStatusDict: [],
			throwStatusDict: [],
			warehoursStatusDict: [],
			ruleForm: {
				makeNo: this.workOrderDetail.makeNo,
				runStatusList: [],
				innerProductNo: this.workOrderDetail.innerProductNo,
				partNo: this.workOrderDetail.partNo,
				batchStatusList: [],
				nowStepCodeList: [],
				warehousStatusList: [],
				workOrderCode: this.workOrderDetail.workOrderCode,
				ngStatusList: [],
				pauseStatusList: [],
				throwStatusList: [],
				time: "",
				routeVersion:""
			},
			batchNavBarList: {
				title: "批次列表",
				list: [
					{
						Tname: "移除批次",
						Tcode: "delete",
					},
					{
						Tname: "清空列表",
						Tcode: "delete",
					},
				],
			},
			qrCode: "",
		};
	},

	created() {
		if (this.mode == "addExistedBatch") {
			this.ruleForm.warehousStatusList = ["NOTSTORAGE"];
			this.ruleForm.throwStatusList = ["BEFED"];
			this.ruleForm.innerProductNo = this.workOrderDetail.innerProductNo;
			this.ruleForm.partNo = this.workOrderDetail.partNo;
			this.ruleForm.makeNo = "";
			this.ruleForm.workOrderCode = "";
		} else {
			if (this.workOrderCodeList.length <= 1) {
				getProductRouteByWorkOrderCode({
					workOrderCode: this.workOrderDetail.workOrderCode,
				}).then((res) => {
					if (res.data) {
						this.stepDict = [];
						res.data.forEach((item) => {
							this.stepDict.push({
								label: item.stepName,
								value: item.stepCode,
							});
						});
					}
				});
			}else{
        this.ruleForm.workOrderCode = this.workOrderCodeList.map(item=>{return item.workOrderCode}).join(',')
        this.ruleForm.makeNo = ""
        this.ruleForm.innerProductNo = ""
        this.ruleForm.partNo = ""
      }
		}
		searchDict({
			typeList: [
				"RUN_STATUS",
				"NG_STATUS",
				"PRODUCTION_BATCH_STATUS_SUB",
				"PAUSE_STATUS",
				"THROW_STATUS",
				"PP_FPI_STATUS",
			],
		}).then((res) => {
			this.batchStatusDict = res.data.PRODUCTION_BATCH_STATUS_SUB;
			this.runStatusDict = res.data.RUN_STATUS;
			this.ngStatusDict = res.data.NG_STATUS;
			this.pauseStatusDict = res.data.PAUSE_STATUS;
			this.throwStatusDict = res.data.THROW_STATUS;
      this.$store.commit("SET_THROW_STATUS", res.data.THROW_STATUS);
			this.warehoursStatusDict = res.data.PP_FPI_STATUS;
      this.$store.commit("SET_WAREHOURS_STATUS", res.data.PP_FPI_STATUS);
			this.$store.commit("SET_PRODUCTION_BATCH_STATUS_SUB", res.data.PRODUCTION_BATCH_STATUS_SUB);
			this.$store.commit("SET_RUN_STATUS", res.data.RUN_STATUS);
			this.$store.commit("SET_NG_STATUS", res.data.NG_STATUS);
			this.$store.commit("SET_PAUSE_STATUS", res.data.PAUSE_STATUS);
		});

		this.table = batchDialogList;
	},
	mounted() {
		this.submit("1");
	},

	methods: {
		openCraft(){
			this.craftData.partNo = this.ruleForm.partNo;
			this.craftData.productNo = this.ruleForm.innerProductNo;
			this.craftFlag = true;
		},
		//根据id获取批次列表
		getBatchList(val) {
			if (val) {
				this.table.count = 1;
			}
			let param = {
				data: {
					innerProductNo: this.ruleForm.innerProductNo,
					partNo: this.ruleForm.partNo,
					makeNo: this.ruleForm.makeNo,
					routeVersion: this.ruleForm.routeVersion,
					workOrderCode: this.ruleForm.workOrderCode,
					statusSubclassList: this.ruleForm.batchStatusList,
					nowStepCodeList: this.ruleForm.nowStepCodeList,
					throwStatusList: this.ruleForm.throwStatusList,
					runStatusList: this.ruleForm.runStatusList,
					pauseStatusList: this.ruleForm.pauseStatusList,
					warehousStatusList: this.ruleForm.warehousStatusList,
					ngStatusList: this.ruleForm.ngStatusList,
					createdTimeStart: !this.ruleForm.time ? null : formatTimesTamp(this.ruleForm.time[0]) || null,
					createdTimeEnd: !this.ruleForm.time ? null : formatTimesTamp(this.ruleForm.time[1]) || null,
				},
				page: {
					pageNumber: this.table.count,
					pageSize: this.table.size,
				},
			};
			productionBatchSearch(param).then((res) => {
				if (res.data.length > 0) {
					this.table.tableData = res.data;
					this.table.total = res.page.total;
					this.table.count = res.page.pageNumber;
					this.table.size = res.page.pageSize;
				} else {
					this.table.tableData = [];
				}
			});
		},
		changeSize(val) {
			this.table.size = val;
			this.submit(true);
		},
		changePages(val) {
			this.table.count = val;
			this.submit();
		},
		checkRow(val) {
			this.rowData = val;
		},
		selectBatchRows(val) {
			this.rowDatas = _.cloneDeep(val);
		},
		submit(val) {
			if (this.mode == "addExistedBatch") {
				this.getBatchList(val);
			} else {
				productionBatchSearchByWorkOrderCode({
          modelFlag:this.mode == 'splitWorkOrder' ? '0': '1',
					routeVersion: this.ruleForm.routeVersion,
          workOrderCodes: this.ruleForm.workOrderCode,
					batchStatusList: this.ruleForm.batchStatusList,
					nowStepCodeList: this.ruleForm.nowStepCodeList,
					throwStatusList: this.ruleForm.throwStatusList,
					runStatusList: this.ruleForm.runStatusList,
					pauseStatusList: this.ruleForm.pauseStatusList,
					warehousStatusList: this.ruleForm.warehousStatusList,
					ngStatusList: this.ruleForm.ngStatusList,
					createdTimeStart: !this.ruleForm.time ? null : formatTimesTamp(this.ruleForm.time[0]) || null,
					createdTimeEnd: !this.ruleForm.time ? null : formatTimesTamp(this.ruleForm.time[1]) || null,
				}).then((res) => {
					this.table.total = 0;
					this.table.tableData = res.data.filter((item) => {
						return !this.addedBatchList.some((filterItem) => {
							return item.batchNumber === filterItem;
						});
					});
				});
			}
		},
		closeMark() {
			this.$emit("update:showBatchListDialog", false);
		},
		submitMark() {
			if (this.rowDatas.length) {
				this.$emit("selectRow", this.rowDatas);
			} else {
				this.$showWarn("请先选择数据");
				return false;
			}
		},
		resetFrom(form) {
			this.$refs[form].resetFields();
		},
		batchNavClick(val) {
			switch (val) {
				case "移除批次":
					if (!this.rowDatas.length) {
						this.$showWarn("请先勾选要移除的批次");
						return;
					}
					this.rowDatas.forEach((item) => {
						this.table.tableData.forEach((tItem, index) => {
							if (item.batchNumber == tItem.batchNumber) {
								this.table.tableData.splice(index, 1);
							}
						});
					});
					break;
				case "清空列表":
					this.table.tableData = [];
					break;

				default:
					break;
			}
		},
		//工艺路线选择回调
		selecrCraftRow(val) {
			console.log(`output->val`,val)
			this.ruleForm.routeVersion = val.routeVersion
		},
		// 扫码批次录入
		qrCodeEnter() {
			getBatchDetailByScan({
				workOrderCode: this.workOrderDetail.workOrderCode,
				batchNumber: this.qrCode,
			}).then((res) => {
				if (res.data) {
					let isHave = this.table.tableData.some((item) => {
						return item.batchNumber === res.data.batchNumber;
					});
					if (isHave) {
						this.$showWarn("当前有批次已在列表中");
					} else {
						this.table.tableData.push(res.data);
					}
				} else {
					this.$showWarn(res.status.message);
				}
			});
		},
		//批次打印
		printBatch() {

      if (!this.rowDatas.length) {
				this.$showWarn("请先勾选要打印的批次");
				return;
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(this.rowDatas));
			let url = location.href.split("/#/")[0];
			window.open(url + "/#/batchList/batchPrint");
		},
		//批次刻字打印
		printLetterBatch() {
			if (!this.rowDatas.length) {
				this.$showWarn("请先勾选要打印的批次");
				return;
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(this.rowDatas));
			let url = location.href.split("/#/")[0];
			window.open(url + "/#/batchList/batchPDF417Print");
		},
    siOrderNoticePrint() {
      if (!this.rowDatas.length) {
				this.$showWarn("请先勾选要打印的批次");
				return;
			}
      this.workOrderCodeList.forEach(item=>{
        this.rowDatas.forEach(tItem=>{
          if(item.workOrderCode == tItem.workOrderCode){
            tItem.workOrderUnit = item.unit
            tItem.workOrderRemark = item.remark
            tItem.workOrderCustomerOrder = item.customerOrder
            tItem.workOrderCustomerName = item.customerName
            tItem.workOrderPlanEndDate = formatYD(item.planEndDate)
          }
        })
      })
      sessionStorage.setItem("siBatchList", JSON.stringify(this.rowDatas));
      window.open(location.href.split("/#/")[0] + "/#/batchList/SIOrderNoticePrint");
		},
	},
};
</script>
